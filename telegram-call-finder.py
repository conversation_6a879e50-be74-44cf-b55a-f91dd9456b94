#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Telegram CPW消息搜索工具

这个脚本用于通过Solanatoken_address作为关键词搜索电报频道中的消息，
查找在指定时间点之后发布的、包含CPW值大于指定阈值的消息。

使用方法:
1. 直接运行脚本，会弹出文件选择对话框
   - 首先选择包含token_address和时间的输入Excel表格
   - 然后选择保存结果的输出Excel表格
2. 或者使用命令行参数运行脚本:
   - 创建示例表格: python telegram-call-finder.py --create-example
   - 不使用图形界面: python telegram-call-finder.py --no-gui --input 输入文件.xlsx --output 输出文件.xlsx
   - 自定义CPW阈值: python telegram-call-finder.py --threshold 300
   - 自定义结果数量: python telegram-call-finder.py --limit 5

输入Excel表格格式:
- 必须包含"token_address"和"added_time"两列
- "token_address"列为Solanatoken_address
- "added_time"列为搜索起始时间，支持多种日期格式

输出结果:
- 会在原表格基础上添加CPW_1, CPW_2, CPW_3等列和对应的消息时间列
- 每个代币最多返回3条结果(可通过--limit参数修改)
"""

import asyncio
import re
import pandas as pd
import datetime
import pytz
import tkinter as tk
from tkinter import filedialog
from telethon import TelegramClient
from telethon.tl.types import InputMessagesFilterEmpty
import argparse
from tqdm import tqdm

# Telegram API配置
API_ID = 24702520
API_HASH = '7b1f880deb5998f36a079cfdbd097534'
SESSION_NAME = 'telegram_cpw_finder_session'

# 要搜索的频道ID (请确保已加入这些频道)
CHANNEL_IDS = [
    -1001914959004,
    -1002050218130
]

# 默认CPW阈值
DEFAULT_CPW_THRESHOLD = 278  # 设置为0以显示所有找到的CPW值

# 默认搜索结果数量限制
DEFAULT_RESULT_LIMIT = 6

def ensure_utc_timezone(date_obj):
    """
    确保日期对象具有UTC时区信息

    Args:
        date_obj (datetime): 日期时间对象

    Returns:
        datetime: 带有UTC时区信息的日期时间对象
    """
    if date_obj.tzinfo is None:
        import pytz
        return pytz.UTC.localize(date_obj)
    else:
        return date_obj

async def extract_cpw_value(message_text):
    """
    从消息文本中提取CPW值

    Args:
        message_text (str): 消息文本

    Returns:
        float or None: 提取到的CPW值，如果未找到则返回None
    """
    # 打印消息文本的前200个字符，用于调试
    print(f"尝试从消息中提取CPW值: {message_text[:200]}...")

    # 处理Markdown链接格式: [**Avg CPW**](https://t.me/CallAnalyserPortal/130)**:** 530.13
    # 这种格式中，530.13是真正的CPW值
    markdown_link_patterns = [
        r'\[\*\*Avg\s*CPW\*\*\].*?\*\*:\*\*\s*(\d+\.?\d*)',  # 完整格式
        r'\*\*:\*\*\s*(\d+\.?\d*)',                          # 只匹配**:** 后面的数字
        r'Avg\s*CPW.*?:\s*(\d+\.?\d*)',                      # 匹配Avg CPW: 后面的数字
        r'\[\*\*Avg\s*CPW\*\*\].*?(\d+\.?\d*)',             # 简化的Markdown格式
        r'Avg\s*CPW.*?(\d+\.?\d*)',                          # 更宽松的Avg CPW匹配
    ]

    for pattern in markdown_link_patterns:
        match = re.search(pattern, message_text, re.IGNORECASE)
        if match:
            try:
                value = float(match.group(1))
                print(f"成功从Markdown链接提取CPW值: {value}")
                return value
            except ValueError:
                continue

    # 首先尝试提取Avg CPW: 数值格式
    avg_cpw_patterns = [
        r'Avg\s*CPW\s*:\s*(\d+\.?\d*)',  # Avg CPW: 453.5
        r'Avg\s*CPW\s*：\s*(\d+\.?\d*)',  # Avg CPW： 453.5 (中文冒号)
        r'Avg\s*CPW\s*=\s*(\d+\.?\d*)',  # Avg CPW = 453.5
        r'Avg\s*CPW\s*(\d+\.?\d*)',      # Avg CPW 453.5
        r'Avg\s*CPW[^\d]*(\d+\.?\d*)',   # Avg CPW后跟任意非数字字符，然后是数字
        r'[Aa]vg\s*[Cc][Pp][Ww][^\d]*(\d+\.?\d*)',  # 不区分大小写的Avg CPW
        r'Average\s*CPW\s*:\s*(\d+\.?\d*)',  # Average CPW: 453.5
        r'Average\s*CPW[^\d]*(\d+\.?\d*)',   # Average CPW后跟任意非数字字符，然后是数字
        r'平均\s*CPW\s*:\s*(\d+\.?\d*)',     # 中文：平均CPW: 453.5
    ]

    for pattern in avg_cpw_patterns:
        match = re.search(pattern, message_text, re.IGNORECASE)
        if match:
            try:
                value = float(match.group(1))
                print(f"成功提取Avg CPW值: {value}")
                return value
            except ValueError:
                continue

    # 如果没有找到Avg CPW，尝试提取普通CPW值
    cpw_patterns = [
        r'CPW\s*:\s*(\d+\.?\d*)',  # CPW: 302.73
        r'CPW\s*：\s*(\d+\.?\d*)',  # CPW： 302.73 (中文冒号)
        r'CPW\s*=\s*(\d+\.?\d*)',  # CPW = 302.73
        r'CPW\s*(\d+\.?\d*)',       # CPW 302.73
        r'CPW[^\d]*(\d+\.?\d*)',    # CPW后跟任意非数字字符，然后是数字
        r'[Cc][Pp][Ww][^\d]*(\d+\.?\d*)',  # 不区分大小写的CPW
        r'Cost\s*Per\s*Wallet\s*:\s*(\d+\.?\d*)',  # Cost Per Wallet: 302.73
        r'Cost\s*Per\s*Wallet[^\d]*(\d+\.?\d*)',   # Cost Per Wallet后跟数字
        r'每钱包成本\s*:\s*(\d+\.?\d*)',            # 中文：每钱包成本: 302.73
        r'钱包成本\s*:\s*(\d+\.?\d*)',              # 中文：钱包成本: 302.73
    ]

    for pattern in cpw_patterns:
        match = re.search(pattern, message_text, re.IGNORECASE)
        if match:
            try:
                value = float(match.group(1))
                print(f"成功提取CPW值: {value}")
                return value
            except ValueError:
                continue

    print("未能从消息中提取CPW值")
    return None

async def search_messages(client, channel_id, token_address, after_date, cpw_threshold, limit, verbose=False):
    """
    在指定频道中搜索包含特定token_address且CPW值大于阈值的消息

    Args:
        client (TelegramClient): Telegram客户端
        channel_id (int): 频道ID
        token_address (str): Solana-token_address
        after_date (datetime): 搜索此日期之后的消息
        cpw_threshold (float): CPW值阈值
        limit (int): 返回结果数量限制
        verbose (bool): 是否显示详细信息，包括消息内容

    Returns:
        list: 符合条件的消息列表，每个元素为(cpw_value, message_date, message_id, time_diff)元组，
             其中time_diff是消息时间与added_time的差值（秒）
    """
    try:
        print(f"\n开始搜索频道 {channel_id} 中包含地址 {token_address} 的消息...")
        print(f"搜索条件: 日期 > {after_date}, CPW值 > {cpw_threshold}")

        # 获取频道实体
        channel = await client.get_entity(channel_id)
        print(f"成功获取频道信息: {getattr(channel, 'title', str(channel_id))}")

        # 搜索包含token_address的消息
        messages = []
        message_count = 0
        found_message_count = 0

        # 首先尝试使用完整地址搜索
        print(f"使用完整地址搜索: {token_address}")
        async for message in client.iter_messages(
            channel,
            search=token_address,
            filter=InputMessagesFilterEmpty(),
            limit=300  # 增加搜索结果数量，以获取更多消息
        ):
            message_count += 1
            # 检查消息日期是否在指定日期之后
            msg_date_utc = message.date
            after_date_utc = ensure_utc_timezone(after_date)

            if msg_date_utc > after_date_utc:
                # 更严格的排除关键词检查 - 只排除明确的汇总消息
                if message.text and (
                    ("Total Call" in message.text and "Summary" in message.text) or
                    ("Main Calls" in message.text and "Summary" in message.text) or
                    ("Daily Summary" in message.text) or
                    ("Weekly Summary" in message.text)
                ):
                    print(f"跳过汇总消息: ID={message.id}")
                    continue

                found_message_count += 1
                print(f"找到消息 ID: {message.id}, 日期: {message.date}")
                if verbose and message.text:
                    print(f"消息内容: {message.text[:500]}..." if len(message.text) > 500 else f"消息内容: {message.text}")
                if message.text:
                    # 提取CPW值
                    cpw_value = await extract_cpw_value(message.text)
                    # 计算时间差（秒）- 确保时区一致性
                    msg_date = message.date  # 保持原始时区信息
                    # 确保时区一致性
                    after_date_utc = ensure_utc_timezone(after_date)
                    time_diff = (msg_date - after_date_utc).total_seconds()

                    if cpw_value is not None:
                        if cpw_value > cpw_threshold:
                            print(f"消息符合条件: CPW值 = {cpw_value} > {cpw_threshold}")
                            messages.append((cpw_value, message.date, message.id, time_diff))

                            # 不再提前停止搜索，收集所有符合条件的消息
                        else:
                            print(f"消息CPW值 = {cpw_value} <= {cpw_threshold}，不符合条件")
                    else:
                        # 如果没有找到CPW值但阈值为0，也保存消息
                        if cpw_threshold <= 0:
                            print(f"未找到CPW值，但由于阈值为{cpw_threshold}，仍然保存消息")
                            messages.append((0, message.date, message.id, time_diff))

                            # 不再提前停止搜索，收集所有符合条件的消息

        print(f"搜索结果: 共找到 {message_count} 条消息，其中 {found_message_count} 条在指定日期之后，{len(messages)} 条符合CPW值条件")

        # 按日期排序，返回距离指定时间点最近的几条消息
        # 确保日期时间对象的时区一致性
        # 打印排序前的消息列表
        print("排序前的消息列表:")
        for msg in messages:
            msg_date = msg[1]  # 保持原始时区信息
            # 确保时区一致性计算时间差
            after_date_utc = ensure_utc_timezone(after_date)
            time_diff = (msg_date - after_date_utc).total_seconds()
            print(f"  消息日期: {msg_date}, 时间差: {time_diff}秒")

        # 按照时间排序（按消息发布时间排序）
        sorted_msgs = sorted(messages, key=lambda x: x[1])

        # 打印排序后的消息列表
        print("排序后的消息列表:")
        for msg in sorted_msgs[:limit]:
            msg_date = msg[1]  # 保持原始时区信息
            # 确保时区一致性计算时间差
            after_date_utc = ensure_utc_timezone(after_date)
            time_diff = (msg_date - after_date_utc).total_seconds()
            print(f"  消息日期: {msg_date}, 时间差: {time_diff}秒")

        return sorted_msgs[:limit]

    except Exception as e:
        print(f"搜索频道 {channel_id} 时出错: {e}")
        import traceback
        traceback.print_exc()
        return []

async def parse_date_string(date_str):
    """
    解析多种格式的日期字符串，确保返回UTC时区的datetime对象

    Args:
        date_str (str): 日期字符串

    Returns:
        datetime: 解析后的日期时间对象（UTC时区）
    """
    import pytz

    # 尝试多种日期格式
    formats = [
        "%Y-%m-%d %H:%M:%S.%f %Z",  # 2025-05-12 01:28:16.000 UTC
        "%Y-%m-%d %H:%M:%S.%f",     # 2025-05-12 01:28:16.000
        "%Y-%m-%d %H:%M:%S %Z",     # 2025-05-12 01:28:16 UTC
        "%Y-%m-%d %H:%M:%S",        # 2025-05-12 01:28:16
        "%Y-%m-%d %H:%M:%S+00:00",  # 2025-05-05 14:43:54+00:00 (API返回格式)
        "%Y-%m-%d %H:%M:%S%z",      # 2025-05-05 14:43:54+0000 (带时区偏移)
        "%Y-%m-%d %H:%M %Z",        # 2025-05-12 01:28 UTC
        "%Y-%m-%d %H:%M",           # 2025-05-12 01:28
        "%Y-%m-%d"                  # 2025-05-12
    ]

    # 首先尝试直接使用datetime的fromisoformat方法（处理ISO 8601格式，如2025-05-05T14:43:54+00:00）
    try:
        # 将T分隔符替换为空格，以便处理2025-05-05T14:43:54+00:00格式
        iso_date_str = date_str.replace(' ', 'T') if 'T' not in date_str else date_str
        parsed_date = datetime.datetime.fromisoformat(iso_date_str)
        # 如果没有时区信息，假设为UTC
        if parsed_date.tzinfo is None:
            parsed_date = pytz.UTC.localize(parsed_date)
        return parsed_date
    except ValueError:
        pass

    # 然后尝试各种格式
    for fmt in formats:
        try:
            parsed_date = datetime.datetime.strptime(date_str, fmt)
            # 如果解析成功但没有时区信息，假设为UTC
            if parsed_date.tzinfo is None:
                # 特殊处理：如果原字符串包含UTC，则设为UTC时区
                if 'UTC' in date_str.upper():
                    parsed_date = pytz.UTC.localize(parsed_date)
                else:
                    # 否则也假设为UTC（因为大多数情况下输入都是UTC时间）
                    parsed_date = pytz.UTC.localize(parsed_date)
            return parsed_date
        except ValueError:
            continue

    # 如果所有格式都不匹配，抛出异常
    raise ValueError(f"无法解析日期字符串: {date_str}")

async def search_all_channels(token_address, after_date_str, cpw_threshold=DEFAULT_CPW_THRESHOLD, limit=DEFAULT_RESULT_LIMIT):
    """
    在所有指定的频道中搜索符合条件的消息

    Args:
        token_address (str): Solanatoken_address
        after_date_str (str): 搜索此日期之后的消息，支持多种日期格式
        cpw_threshold (float): CPW值阈值
        limit (int): 返回结果数量限制

    Returns:
        list: 符合条件的消息列表，每个元素为(cpw_value, message_date)元组
    """
    # 解析日期字符串
    after_date = await parse_date_string(after_date_str)

    # 创建客户端
    client = TelegramClient(SESSION_NAME, API_ID, API_HASH)
    await client.start()

    try:
        all_results = []

        # 搜索每个频道，传递一个较大的limit值以获取更多结果
        for channel_id in CHANNEL_IDS:
            results = await search_messages(client, channel_id, token_address, after_date, cpw_threshold, 100)  # 使用较大的limit值
            all_results.extend(results)

        # 按日期排序，返回距离指定时间点最近的几条消息
        # 确保日期时间对象的时区一致性
        # 按照时间排序，获取最早的几条消息
        return sorted(all_results, key=lambda x: x[1])[:limit]

    finally:
        await client.disconnect()

async def process_token_list(input_file, output_file=None, cpw_threshold=DEFAULT_CPW_THRESHOLD, limit=DEFAULT_RESULT_LIMIT, verbose=False):
    """
    处理包含token_address和时间的Excel表格

    Args:
        input_file (str): 输入Excel文件路径
        output_file (str, optional): 输出Excel文件路径，默认覆盖输入文件
        cpw_threshold (float): CPW值阈值
        limit (int): 每个代币返回的结果数量限制
        verbose (bool): 是否显示详细信息，包括消息内容
    """
    # 读取Excel文件
    df = pd.read_excel(input_file)

    # 检查必要的列是否存在
    required_columns = ['token_address', 'added_time']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"输入文件缺少必要的列: {', '.join(missing_columns)}")

    # 创建客户端
    client = TelegramClient(SESSION_NAME, API_ID, API_HASH)
    await client.start()

    try:
        # 为结果创建新列
        for i in range(1, limit + 1):
            if f'CPW_{i}' not in df.columns:
                df[f'CPW_{i}'] = None
            if f'消息时间_{i}' not in df.columns:
                df[f'消息时间_{i}'] = None
            if f'时间差_{i}' not in df.columns:
                df[f'时间差_{i}'] = None

        # 处理每一行
        for index, row in tqdm(df.iterrows(), total=len(df), desc="处理代币"):
            token_address = row['token_address']
            after_date_str = row['added_time']

            # 跳过空值
            if pd.isna(token_address) or pd.isna(after_date_str):
                continue

            # 解析日期字符串
            try:
                after_date = await parse_date_string(after_date_str)
            except ValueError as e:
                print(f"行 {index+1}: {str(e)}")
                continue

            # 搜索所有频道，传递一个较大的limit值以获取更多结果
            all_results = []
            for channel_id in CHANNEL_IDS:
                results = await search_messages(client, channel_id, token_address, after_date, cpw_threshold, 100, verbose)  # 使用较大的limit值
                all_results.extend(results)

            # 按日期排序，获取距离指定时间点最近的几条消息
            # 确保日期时间对象的时区一致性

            # 打印排序前的消息列表
            print("\n所有搜索结果:")
            for i, msg in enumerate(all_results):
                msg_date = msg[1]  # 保持原始时区信息
                # 确保时区一致性计算时间差
                after_date_utc = ensure_utc_timezone(after_date)
                time_diff = (msg_date - after_date_utc).total_seconds()
                print(f"  结果 {i+1}: 日期={msg_date}, 时间差={time_diff}秒, CPW={msg[0]}")

            # 按照时间排序，获取最早的几条消息
            sorted_results = sorted(all_results, key=lambda x: x[1])[:limit]

            # 打印排序后的消息列表
            print("\n排序后的结果:")
            for i, msg in enumerate(sorted_results):
                msg_date = msg[1]  # 保持原始时区信息
                # 确保时区一致性计算时间差
                after_date_utc = ensure_utc_timezone(after_date)
                time_diff = (msg_date - after_date_utc).total_seconds()
                print(f"  结果 {i+1}: 日期={msg_date}, 时间差={time_diff}秒, CPW={msg[0]}")

            # 更新DataFrame
            for i, result in enumerate(sorted_results, 1):
                if i <= limit:
                    cpw_value, message_date, _, time_diff = result
                    df.at[index, f'CPW_{i}'] = cpw_value
                    df.at[index, f'消息时间_{i}'] = message_date.strftime("%Y-%m-%d %H:%M:%S")
                    df.at[index, f'时间差_{i}'] = time_diff

            # 每处理完一个代币后等待2.7秒
            if index < len(df) - 1:  # 如果不是最后一个代币
                print(f"处理完代币 {token_address}，等待2.7秒后继续...")
                await asyncio.sleep(0.6)

        # 保存结果
        output_path = output_file if output_file else input_file
        df.to_excel(output_path, index=False)
        print(f"结果已保存到: {output_path}")

    finally:
        await client.disconnect()

def create_example_excel(file_path):
    """
    创建示例Excel文件

    Args:
        file_path (str): 文件保存路径
    """
    # 创建示例数据
    data = {
        'token_address': [
            '98CRSK82tAzc3Pcm4EN9XBFHHPgZNzvaFobKGULfYBfs',
            'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
            '7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU'
        ],
        'added_time': [
            '2025-05-12 01:28:16.000 UTC',
            '2025-05-15 00:00:00 UTC',
            '2025-05-10 12:30:00 UTC'
        ]
    }

    # 创建DataFrame并保存为Excel
    df = pd.DataFrame(data)
    df.to_excel(file_path, index=False)
    print(f"示例Excel文件已创建: {file_path}")

async def select_file_dialog(title="选择文件", filetypes=(("Excel文件", "*.xlsx"), ("所有文件", "*.*"))):
    """
    显示文件选择对话框

    Args:
        title (str): 对话框标题
        filetypes (tuple): 文件类型过滤器

    Returns:
        str: 选择的文件路径，如果取消则返回None
    """
    # 创建隐藏的根窗口
    root = tk.Tk()
    root.withdraw()

    # 显示文件选择对话框
    file_path = filedialog.askopenfilename(title=title, filetypes=filetypes)

    # 销毁根窗口
    root.destroy()

    return file_path if file_path else None

async def save_file_dialog(title="保存文件", defaultextension=".xlsx", filetypes=(("Excel文件", "*.xlsx"), ("所有文件", "*.*"))):
    """
    显示文件保存对话框

    Args:
        title (str): 对话框标题
        defaultextension (str): 默认文件扩展名
        filetypes (tuple): 文件类型过滤器

    Returns:
        str: 保存的文件路径，如果取消则返回None
    """
    # 创建隐藏的根窗口
    root = tk.Tk()
    root.withdraw()

    # 显示文件保存对话框
    file_path = filedialog.asksaveasfilename(title=title, defaultextension=defaultextension, filetypes=filetypes)

    # 销毁根窗口
    root.destroy()

    return file_path if file_path else None

async def debug_search(token_address, after_date_str, limit=100, cpw_threshold=0):
    """
    调试搜索功能

    Args:
        token_address (str): token_address
        after_date_str (str): 搜索此日期之后的消息
        limit (int): 搜索结果数量限制
        cpw_threshold (float): CPW值阈值
    """
    print(f"开始调试搜索功能...")
    print(f"token_address: {token_address}")
    print(f"added_time: {after_date_str}")
    print(f"CPW阈值: {cpw_threshold}")

    try:
        # 解析日期
        after_date = await parse_date_string(after_date_str)
        print(f"解析后的日期: {after_date}")

        # 创建客户端
        client = TelegramClient(SESSION_NAME, API_ID, API_HASH)
        await client.start()

        try:
            # 搜索每个频道
            for i, channel_id in enumerate(CHANNEL_IDS):
                print(f"\n开始搜索频道 {channel_id}...")

                try:
                    # 获取频道实体
                    channel = await client.get_entity(channel_id)
                    print(f"成功获取频道信息: {getattr(channel, 'title', str(channel_id))}")

                    # 搜索消息
                    message_count = 0
                    found_message_count = 0
                    cpw_message_count = 0

                    print(f"使用关键词搜索: {token_address}")
                    async for message in client.iter_messages(
                        channel,
                        search=token_address,
                        limit=300  # 增加搜索结果数量，以获取更多消息
                    ):
                        message_count += 1

                        # 检查消息日期
                        msg_date_utc = message.date
                        after_date_utc = ensure_utc_timezone(after_date)
                        print(f"消息 ID: {message.id}, 日期: {message.date}")

                        if msg_date_utc > after_date_utc:
                            found_message_count += 1
                            print(f"  ✓ 日期符合条件: {msg_date_utc} > {after_date_utc}")

                            # 显示消息内容
                            if message.text:
                                print(f"  消息内容: {message.text}")

                                # 提取CPW值
                                print(f"  尝试提取CPW值...")
                                # 尝试匹配特定模式
                                for pattern in [
                                    r'\[\*\*Avg\s*CPW\*\*\].*?\*\*:\*\*\s*(\d+\.?\d*)',
                                    r'\*\*:\*\*\s*(\d+\.?\d*)',
                                    r'Avg\s*CPW.*?:\s*(\d+\.?\d*)'
                                ]:
                                    match = re.search(pattern, message.text, re.IGNORECASE)
                                    if match:
                                        print(f"  匹配到模式: {pattern}")
                                        print(f"  匹配结果: {match.group(0)}")
                                        print(f"  提取的值: {match.group(1)}")

                                # 正常提取CPW值
                                cpw_value = await extract_cpw_value(message.text)
                                if cpw_value is not None:
                                    if cpw_value > cpw_threshold:
                                        cpw_message_count += 1
                                        print(f"  ✓ CPW值符合条件: {cpw_value} > {cpw_threshold}")
                                    else:
                                        print(f"  ✗ CPW值不符合条件: {cpw_value} <= {cpw_threshold}")
                                else:
                                    print(f"  ✗ 未找到CPW值")
                        else:
                            print(f"  ✗ 日期不符合条件: {msg_date_utc} <= {after_date_utc}")

                    print(f"\n搜索结果摘要:")
                    print(f"- 共找到 {message_count} 条包含关键词的消息")
                    print(f"- 其中 {found_message_count} 条在指定日期之后")
                    print(f"- 其中 {cpw_message_count} 条CPW值大于阈值")

                    # 如果不是最后一个频道，等待2.7秒
                    if i < len(CHANNEL_IDS) - 1:
                        print(f"处理完频道 {channel_id}，等待2.7秒后继续...")
                        await asyncio.sleep(0.6)

                except Exception as e:
                    print(f"搜索频道 {channel_id} 时出错: {e}")
                    import traceback
                    traceback.print_exc()

        finally:
            await client.disconnect()

    except Exception as e:
        print(f"调试搜索时出错: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='搜索Telegram频道中的CPW消息')
    parser.add_argument('--threshold', '-t', type=float, default=DEFAULT_CPW_THRESHOLD, help=f'CPW值阈值 (默认: {DEFAULT_CPW_THRESHOLD})')
    parser.add_argument('--limit', '-l', type=int, default=DEFAULT_RESULT_LIMIT, help=f'每个代币返回的结果数量限制 (默认: {DEFAULT_RESULT_LIMIT})')
    parser.add_argument('--create-example', action='store_true', help='创建示例Excel文件')
    parser.add_argument('--no-gui', action='store_true', help='不使用图形界面选择文件')
    parser.add_argument('--input', '-i', help='输入Excel文件路径 (仅在--no-gui模式下使用)')
    parser.add_argument('--output', '-o', help='输出Excel文件路径 (仅在--no-gui模式下使用)')
    parser.add_argument('--example-path', default='token_search_example.xlsx', help='示例Excel文件保存路径')
    parser.add_argument('--verbose', '-v', action='store_true', help='显示详细信息，包括消息内容')
    parser.add_argument('--save-all', '-a', action='store_true', help='保存所有找到的消息，即使没有CPW值')
    parser.add_argument('--debug', '-d', action='store_true', help='调试搜索功能')
    parser.add_argument('--address', help='调试模式下使用的token_address')
    parser.add_argument('--date', help='调试模式下使用的added_time')

    args = parser.parse_args()

    # 调试搜索功能
    if args.debug:
        if not args.address:
            parser.error("调试模式下必须提供token_address (--address)")
        if not args.date:
            parser.error("调试模式下必须提供added_time (--date)")

        # 如果用户选择保存所有消息，将阈值设为-1
        threshold = -1 if args.save_all else args.threshold

        await debug_search(args.address, args.date, limit=100, cpw_threshold=threshold)
        return

    # 创建示例Excel文件
    if args.create_example:
        example_path = args.example_path
        if not args.no_gui:
            # 使用图形界面选择保存路径
            save_path = await save_file_dialog(
                title="保存示例Excel文件",
                defaultextension=".xlsx",
                filetypes=(("Excel文件", "*.xlsx"), ("所有文件", "*.*"))
            )
            if save_path:
                example_path = save_path
            else:
                print("操作已取消")
                return

        create_example_excel(example_path)
        return

    # 获取输入文件路径
    input_file = args.input
    if not args.no_gui and not input_file:
        # 使用图形界面选择输入文件
        input_file = await select_file_dialog(
            title="选择输入Excel文件",
            filetypes=(("Excel文件", "*.xlsx"), ("所有文件", "*.*"))
        )
        if not input_file:
            print("未选择输入文件，操作已取消")
            return

    # 检查输入文件
    if not input_file:
        parser.error("请提供输入Excel文件路径")

    # 获取输出文件路径
    output_file = args.output
    if not args.no_gui and not output_file:
        # 使用图形界面选择输出文件
        output_file = await save_file_dialog(
            title="选择输出Excel文件",
            defaultextension=".xlsx",
            filetypes=(("Excel文件", "*.xlsx"), ("所有文件", "*.*"))
        )
        # 如果用户取消，则使用输入文件路径作为输出路径
        if not output_file:
            output_file = input_file

    # 如果用户选择保存所有消息，将阈值设为-1
    threshold = -1 if args.save_all else args.threshold

    # 处理代币列表
    await process_token_list(input_file, output_file, threshold, args.limit, args.verbose)

if __name__ == "__main__":
    asyncio.run(main())