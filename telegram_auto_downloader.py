#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Telegram消息自动下载器
根据Dune数据的时间范围自动下载指定群组的消息
预计下载时间：约10分钟

功能：
1. 读取Dune CSV文件，分析added_time列的时间范围
2. 自动下载对应日期范围的群组消息
3. 保存为JSON格式，兼容现有的telegram_to_excel.py
4. 显示详细的下载进度和时间估算
"""

import asyncio
import json
import os
import pandas as pd
from datetime import datetime, timedelta
from telethon import TelegramClient
# from telethon.tl.types import InputMessagesFilterEmpty  # 不再需要
import argparse
import time

# 导入配置
from config import config

class TelegramAutoDownloader:
    def __init__(self):
        """初始化Telegram自动下载器"""
        self.api_id = config.TELEGRAM_CONFIG['api_id']
        self.api_hash = config.TELEGRAM_CONFIG['api_hash']
        self.session_name = config.TELEGRAM_CONFIG['session_name']

        # 指定的群组ID
        self.target_channel_id = -1002327446504

        self.client = None

    async def connect(self):
        """连接到Telegram"""
        print("🔗 正在连接到Telegram...")
        self.client = TelegramClient(self.session_name, self.api_id, self.api_hash)
        await self.client.start()
        print("✅ 已连接到Telegram")

    async def disconnect(self):
        """断开Telegram连接"""
        if self.client:
            await self.client.disconnect()
            print("✅ 已断开Telegram连接")

    def analyze_dune_timerange(self, dune_csv_file):
        """
        分析Dune数据的时间范围

        Args:
            dune_csv_file (str): Dune CSV文件路径

        Returns:
            tuple: (start_date, end_date) 日期范围
        """
        print(f"📊 分析Dune数据时间范围: {dune_csv_file}")

        try:
            # 读取CSV文件
            df = pd.read_csv(dune_csv_file)

            if 'added_time' not in df.columns:
                raise ValueError("Dune数据中缺少 'added_time' 列")

            print(f"  - 总记录数: {len(df)}")
            print(f"  - added_time列样本: {df['added_time'].head(3).tolist()}")

            # 解析时间列
            df['added_time_parsed'] = pd.to_datetime(df['added_time'], errors='coerce')
            valid_times = df['added_time_parsed'].dropna()

            if len(valid_times) == 0:
                raise ValueError("没有有效的时间数据")

            print(f"  - 有效时间记录: {len(valid_times)}")

            # 获取最早和最晚的日期（精确到日）
            min_datetime = valid_times.min()
            max_datetime = valid_times.max()

            # 转换为日期（去掉时间部分）
            start_date = min_datetime.date()
            end_date = max_datetime.date()

            print(f"📅 确定的时间范围:")
            print(f"  - 最早时间: {min_datetime}")
            print(f"  - 最晚时间: {max_datetime}")
            print(f"  - 下载日期范围: {start_date} 到 {end_date}")
            print(f"  - 总天数: {(end_date - start_date).days + 1} 天")

            return start_date, end_date

        except Exception as e:
            print(f"❌ 分析Dune数据失败: {e}")
            raise

    async def download_messages_by_daterange(self, start_date, end_date):
        """
        下载指定日期范围的群组消息

        Args:
            start_date (date): 开始日期
            end_date (date): 结束日期

        Returns:
            list: 消息列表
        """
        try:
            print(f"\n📥 开始下载群组消息...")
            print(f"群组ID: {self.target_channel_id}")
            print(f"日期范围: {start_date} 到 {end_date}")

            # 获取群组实体
            channel = await self.client.get_entity(self.target_channel_id)
            channel_title = getattr(channel, 'title', str(self.target_channel_id))
            print(f"群组名称: {channel_title}")

            # 转换日期为datetime（包含完整的一天）
            start_datetime = datetime.combine(start_date, datetime.min.time())
            end_datetime = datetime.combine(end_date, datetime.max.time())

            messages = []
            message_count = 0
            self.start_time = time.time()
            last_progress_time = self.start_time

            # 估算总消息数（粗略估计：每天50-200条消息）
            total_days = (end_date - start_date).days + 1
            estimated_total = total_days * 100  # 粗略估计

            # 使用成功验证的方法：从结束日期+1天开始往前获取
            offset_start_date = end_date + timedelta(days=1)
            offset_start_datetime = datetime.combine(offset_start_date, datetime.max.time())

            print(f"⏳ 开始下载消息（预计约 {estimated_total} 条）...")
            print(f"🎯 从 {offset_start_date} 开始往前获取...")
            print(f"📊 进度显示：每100条消息更新一次")

            # 使用成功验证的方法（不使用reverse=True）
            print("🔄 开始遍历消息...")
            message_iter_count = 0  # 遍历的总消息数

            try:
                async for message in self.client.iter_messages(
                    channel,
                    offset_date=offset_start_datetime,  # 从结束日期+1天开始
                    limit=estimated_total * 2  # 保守估计，确保覆盖完整范围
                ):
                    message_iter_count += 1
                    message_date = message.date.replace(tzinfo=None)

                    # 每1000条消息显示遍历进度
                    if message_iter_count % 1000 == 0:
                        print(f"  🔍 已遍历: {message_iter_count:,} 条消息，当前日期: {message_date.date()}")

                    # 检查是否超出时间范围
                    if message_date < start_datetime:
                        print(f"  ⏹️ 到达时间范围下限，停止下载。最后消息日期: {message_date.date()}")
                        break

                    if start_datetime <= message_date <= end_datetime:
                        # 转换消息为字典格式
                        try:
                            message_data = await self.convert_message_to_dict(message)
                            messages.append(message_data)
                            message_count += 1

                            # 显示详细进度（每100条消息）
                            if message_count % 100 == 0:
                                current_time = time.time()
                                elapsed = current_time - self.start_time

                                current_date = message_date.date()
                                try:
                                    remaining_time = self.estimate_remaining_time(
                                        message_count, estimated_total, elapsed
                                    )
                                    elapsed_str = self.format_elapsed_time(elapsed)
                                except Exception as e:
                                    remaining_time = "计算错误"
                                    elapsed_str = f"{elapsed:.1f}秒"
                                    print(f"  ⚠️ 时间计算错误: {e}")

                                print(f"  📅 {current_date} | 已下载: {message_count:,} 条 | "
                                      f"用时: {elapsed_str} | "
                                      f"预计剩余: {remaining_time}")

                                last_progress_time = current_time

                            # 每10条消息显示简单进度
                            elif message_count % 10 == 0:
                                print(f"  📝 已下载: {message_count} 条消息，当前日期: {message_date.date()}")

                        except Exception as e:
                            print(f"  ⚠️ 处理消息 {message.id} 时出错: {e}")
                            continue

                    # 添加延迟避免API限制
                    if message_iter_count % 50 == 0:
                        await asyncio.sleep(0.5)  # 减少延迟时间

            except Exception as e:
                print(f"❌ 消息遍历过程中出错: {e}")
                import traceback
                traceback.print_exc()

            print(f"📊 遍历完成统计:")
            print(f"  - 总遍历消息数: {message_iter_count:,} 条")
            print(f"  - 符合条件消息数: {message_count:,} 条")

            total_time = time.time() - self.start_time
            print(f"\n✅ 下载完成!")
            print(f"📊 最终统计:")
            print(f"  - 总消息数: {len(messages):,} 条")
            print(f"  - 总用时: {self.format_elapsed_time(total_time)}")
            print(f"  - 平均速度: {len(messages)/total_time:.1f} 条/秒")

            return messages

        except Exception as e:
            print(f"❌ 下载消息时出错: {e}")
            import traceback
            traceback.print_exc()
            return []

    def estimate_remaining_time(self, current_count, estimated_total, elapsed_time):
        """
        估算剩余下载时间

        Args:
            current_count (int): 当前已下载消息数
            estimated_total (int): 预计总消息数
            elapsed_time (float): 已用时间（秒）

        Returns:
            str: 格式化的剩余时间字符串
        """
        if current_count == 0:
            return "计算中..."

        # 计算平均速度（消息/秒）
        avg_speed = current_count / elapsed_time

        # 估算剩余消息数
        remaining_messages = max(0, estimated_total - current_count)

        # 估算剩余时间
        if avg_speed > 0:
            remaining_seconds = remaining_messages / avg_speed
            return self.format_elapsed_time(remaining_seconds)
        else:
            return "未知"

    def format_elapsed_time(self, seconds):
        """
        格式化时间显示

        Args:
            seconds (float): 秒数

        Returns:
            str: 格式化的时间字符串
        """
        if seconds < 60:
            return f"{seconds:.0f}秒"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.1f}分钟"
        else:
            hours = seconds / 3600
            return f"{hours:.1f}小时"

    def estimate_remaining_time(self, current_count, estimated_total, elapsed_time):
        """
        估算剩余下载时间

        Args:
            current_count (int): 当前已下载消息数
            estimated_total (int): 预计总消息数
            elapsed_time (float): 已用时间（秒）

        Returns:
            str: 格式化的剩余时间字符串
        """
        if current_count == 0:
            return "计算中..."

        # 计算平均速度（消息/秒）
        avg_speed = current_count / elapsed_time

        # 估算剩余消息数
        remaining_messages = max(0, estimated_total - current_count)

        # 估算剩余时间
        if avg_speed > 0:
            remaining_seconds = remaining_messages / avg_speed
            return self.format_elapsed_time(remaining_seconds)
        else:
            return "未知"

    def format_elapsed_time(self, seconds):
        """
        格式化时间显示

        Args:
            seconds (float): 秒数

        Returns:
            str: 格式化的时间字符串
        """
        if seconds < 60:
            return f"{seconds:.0f}秒"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.1f}分钟"
        else:
            hours = seconds / 3600
            return f"{hours:.1f}小时"

    async def convert_message_to_dict(self, message):
        """
        将Telegram消息转换为字典格式（兼容telegram_to_excel.py）
        正确处理文本实体，生成与手动导出相同的格式

        Args:
            message: Telegram消息对象

        Returns:
            dict: 消息字典
        """
        # 处理消息文本和实体 - 暂时简化处理，只保留纯文本
        text_data = []
        text_entities = []

        if message.text:
            # 暂时只保留纯文本，避免格式化解析错误
            text_data = [message.text]

        # 处理发送者信息
        from_info = None
        from_id = None
        if message.sender:
            from_info = getattr(message.sender, 'username', None) or getattr(message.sender, 'first_name', None)
            from_id = f"user{message.sender_id}"

        message_dict = {
            "id": message.id,
            "type": "message",
            "date": message.date.isoformat(),
            "date_unixtime": int(message.date.timestamp()),
            "from": from_info,
            "from_id": from_id,
            "text": text_data,
            "text_entities": text_entities,  # 保持兼容性，但主要信息在text数组中
            "forwarded_from": None,
            "reply_to_message_id": message.reply_to_msg_id,
            "media_type": None,
            "file": None,
            "thumbnail": None,
            "mime_type": None,
            "duration_seconds": None,
            "width": None,
            "height": None,
            "sticker_emoji": None
        }

        # 处理转发消息
        if message.forward:
            message_dict["forwarded_from"] = "forwarded"

        # 处理媒体
        if message.media:
            message_dict["media_type"] = type(message.media).__name__

        return message_dict

    def build_formatted_text_array(self, text, entities):
        """
        构建包含格式化信息的文本数组，与手动导出的格式一致

        Args:
            text (str): 原始文本
            entities (list): Telegram文本实体列表

        Returns:
            list: 格式化的文本数组
        """
        if not entities:
            return [text]

        # 简化处理：只处理非重叠的主要格式化实体
        # 按偏移量排序实体，并过滤掉重叠的实体
        sorted_entities = sorted(entities, key=lambda e: e.offset)

        # 过滤重叠实体，保留最重要的格式化
        filtered_entities = []
        last_end = 0

        for entity in sorted_entities:
            entity_type = type(entity).__name__

            # 只处理主要的格式化类型
            if entity_type in ['MessageEntityBold', 'MessageEntityCode', 'MessageEntityItalic',
                              'MessageEntityUnderline', 'MessageEntityStrike']:
                # 检查是否与之前的实体重叠
                if entity.offset >= last_end:
                    filtered_entities.append(entity)
                    last_end = entity.offset + entity.length

        if not filtered_entities:
            return [text]

        result = []
        last_offset = 0

        for entity in filtered_entities:
            # 添加实体前的普通文本
            if entity.offset > last_offset:
                plain_text = text[last_offset:entity.offset]
                if plain_text:
                    result.append(plain_text)

            # 提取实体文本
            entity_text = text[entity.offset:entity.offset + entity.length]
            entity_type = type(entity).__name__

            # 根据实体类型创建格式化对象
            if entity_type == 'MessageEntityBold':
                result.append({
                    "type": "bold",
                    "text": entity_text
                })
            elif entity_type == 'MessageEntityCode':
                result.append({
                    "type": "code",
                    "text": entity_text
                })
            elif entity_type == 'MessageEntityItalic':
                result.append({
                    "type": "italic",
                    "text": entity_text
                })
            elif entity_type == 'MessageEntityUnderline':
                result.append({
                    "type": "underline",
                    "text": entity_text
                })
            elif entity_type == 'MessageEntityStrike':
                result.append({
                    "type": "strikethrough",
                    "text": entity_text
                })
            else:
                # 其他类型作为普通文本处理
                result.append(entity_text)

            last_offset = entity.offset + entity.length

        # 添加最后剩余的普通文本
        if last_offset < len(text):
            remaining_text = text[last_offset:]
            if remaining_text:
                result.append(remaining_text)

        return result

    async def auto_download_from_dune(self, dune_csv_file, output_file=None):
        """
        根据Dune数据自动下载Telegram消息

        Args:
            dune_csv_file (str): Dune CSV文件路径
            output_file (str, optional): 输出JSON文件路径

        Returns:
            dict: 下载结果
        """
        try:
            # 1. 分析Dune数据的时间范围
            start_date, end_date = self.analyze_dune_timerange(dune_csv_file)

            # 2. 下载对应时间范围的消息
            messages = await self.download_messages_by_daterange(start_date, end_date)

            if not messages:
                return {
                    'success': False,
                    'error': '没有下载到任何消息'
                }

            # 3. 构建输出数据结构（兼容telegram_to_excel.py）
            output_data = {
                "name": f"TokenGraduationGroup_{start_date}_{end_date}",
                "type": "supergroup",
                "id": abs(self.target_channel_id),
                "messages": messages
            }

            # 4. 保存到文件
            if output_file is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_file = config.get_file_path(
                    f"telegram_graduation_messages_{timestamp}.json",
                    config.DATA_DIR
                )

            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)

            # 5. 生成统计报告
            print(f"\n📁 消息已保存到: {output_file}")
            print(f"📊 下载统计:")
            print(f"  - 群组ID: {self.target_channel_id}")
            print(f"  - 时间范围: {start_date} 到 {end_date}")
            print(f"  - 总消息数: {len(messages)}")
            print(f"  - 文件大小: {os.path.getsize(output_file) / 1024:.1f} KB")

            # 按日期统计消息数量
            daily_stats = {}
            for msg in messages:
                msg_date = datetime.fromisoformat(msg['date'].replace('Z', '+00:00')).date()
                daily_stats[msg_date] = daily_stats.get(msg_date, 0) + 1

            print(f"  - 每日消息统计:")
            for date, count in sorted(daily_stats.items()):
                print(f"    {date}: {count} 条")

            return {
                'success': True,
                'output_file': output_file,
                'total_messages': len(messages),
                'date_range': f"{start_date} 到 {end_date}",
                'daily_stats': daily_stats,
                'dune_source': dune_csv_file
            }

        except Exception as e:
            print(f"❌ 自动下载失败: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': str(e)
            }

def find_latest_dune_file():
    """自动查找最新的Dune数据文件"""
    # 查找可能的文件路径
    possible_paths = [
        "./output/",
        "./",
        "./data/"
    ]

    dune_files = []
    for path in possible_paths:
        if os.path.exists(path):
            for file in os.listdir(path):
                if file.startswith('dune_query_result') and file.endswith('.csv'):
                    full_path = os.path.join(path, file)
                    dune_files.append((full_path, os.path.getmtime(full_path)))

    if dune_files:
        # 返回最新的文件
        latest_file = max(dune_files, key=lambda x: x[1])[0]
        return latest_file

    return None

async def main():
    """主函数"""
    # 尝试解析命令行参数，如果失败则使用IDE模式
    dune_file = None
    output_file = None

    try:
        parser = argparse.ArgumentParser(description='Telegram消息自动下载器')
        parser.add_argument('--dune-file', '-d', help='Dune CSV文件路径（可选，会自动查找最新文件）')
        parser.add_argument('--output', '-o', help='输出JSON文件路径')

        args = parser.parse_args()
        dune_file = args.dune_file
        output_file = args.output

    except SystemExit:
        # IDE运行时没有命令行参数，使用默认值
        print("💡 检测到IDE运行模式，使用自动查找文件...")

    # 如果没有指定文件，自动查找最新的Dune文件
    if not dune_file:
        dune_file = find_latest_dune_file()
        if not dune_file:
            print("❌ 未找到Dune数据文件！")
            print("请确保以下位置存在dune_query_result*.csv文件：")
            print("  - ./output/")
            print("  - ./")
            print("  - ./data/")
            print("\n或者使用命令行参数指定文件：")
            print("  python telegram_auto_downloader.py --dune-file your_file.csv")
            return
        else:
            print(f"✅ 自动找到Dune文件: {dune_file}")

    print("🚀 Telegram消息自动下载器")
    print("=" * 50)
    print(f"目标群组ID: -1002327446504")
    print(f"Dune数据文件: {dune_file}")
    print(f"预计下载时间: 约 10 分钟")
    print("=" * 50)

    # 检查输入文件是否存在
    if not os.path.exists(dune_file):
        print(f"❌ Dune文件不存在: {dune_file}")
        return

    downloader = TelegramAutoDownloader()

    try:
        # 连接到Telegram
        await downloader.connect()

        # 执行自动下载
        result = await downloader.auto_download_from_dune(dune_file, output_file)

        if result['success']:
            print(f"\n🎉 自动下载完成!")
            print(f"📁 输出文件: {result['output_file']}")
            print(f"📊 总消息数: {result['total_messages']}")
            print(f"📅 时间范围: {result['date_range']}")
            print(f"\n💡 下一步: 运行 telegram_to_excel.py 处理下载的JSON文件")
        else:
            print(f"\n❌ 下载失败: {result['error']}")

    except Exception as e:
        print(f"\n💥 程序执行出错: {e}")
        import traceback
        traceback.print_exc()

    finally:
        await downloader.disconnect()

if __name__ == "__main__":
    asyncio.run(main())
