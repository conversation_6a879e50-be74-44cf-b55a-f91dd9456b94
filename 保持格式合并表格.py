import openpyxl
import tkinter as tk
from tkinter import filedialog, messagebox
import os

def select_file(title):
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    file_path = filedialog.askopenfilename(title=title, filetypes=[("Excel files", "*.xlsx *.xls")])
    return file_path

try:
    print("请选择包含两个工作表的Excel文件...")
    excel_file = select_file("选择Excel文件")

    if not excel_file:
        print("未选择文件，程序退出。")
        exit()

    print(f"选择的文件: {excel_file}")

    # 使用openpyxl读取Excel文件
    print("正在读取文件...")
    workbook = openpyxl.load_workbook(excel_file)
    
    # 获取工作表名称
    sheet_names = workbook.sheetnames
    if len(sheet_names) < 2:
        print("Excel文件必须至少包含两个工作表！")
        exit()
    
    # 显示可用的工作表
    print("可用的工作表:")
    for i, name in enumerate(sheet_names):
        print(f"{i+1}. {name}")
    
    # 让用户选择工作表
    print("\n请选择第一个工作表（保留格式的表格）:")
    sheet1_idx = int(input("输入工作表编号: ")) - 1
    
    print("请选择第二个工作表（需要合并的数据）:")
    sheet2_idx = int(input("输入工作表编号: ")) - 1
    
    # 验证工作表索引
    if sheet1_idx < 0 or sheet1_idx >= len(sheet_names) or sheet2_idx < 0 or sheet2_idx >= len(sheet_names):
        print("工作表编号无效！")
        exit()
    
    sheet1 = workbook[sheet_names[sheet1_idx]]
    sheet2 = workbook[sheet_names[sheet2_idx]]
    
    # 获取列名
    headers1 = [cell.value for cell in sheet1[1]]
    headers2 = [cell.value for cell in sheet2[1]]
    
    print(f"表格1列名: {', '.join(str(h) for h in headers1 if h)}")
    print(f"表格2列名: {', '.join(str(h) for h in headers2 if h)}")

    print("请选择表格1中的匹配列...")
    match_col1 = input("输入列名: ").strip()
    
    print("请选择表格2中的匹配列...")
    match_col2 = input("输入列名: ").strip()
    
    # 验证输入的列名是否存在
    if match_col1 not in headers1:
        print(f"错误：表格1中不存在列 '{match_col1}'")
        exit()
    if match_col2 not in headers2:
        print(f"错误：表格2中不存在列 '{match_col2}'")
        exit()
    
    # 获取列索引
    match_col1_idx = headers1.index(match_col1) + 1  # Excel列从1开始
    match_col2_idx = headers2.index(match_col2) + 1
    
    # 获取表格2中需要添加到表格1的列（除了匹配列）
    add_cols = [col for col in headers2 if col != match_col2]
    add_cols_idx = [headers2.index(col) + 1 for col in add_cols]
    
    print(f"将添加以下列到表格1: {', '.join(str(col) for col in add_cols if col)}")
    
    # 为表格1添加新列
    for col in add_cols:
        if col not in headers1:  # 如果列不存在，则添加
            sheet1.cell(row=1, column=len(headers1) + 1).value = col
            headers1.append(col)
    
    # 创建表格2的匹配列值到行索引的映射
    match_values_map = {}
    for row_idx in range(2, sheet2.max_row + 1):  # 从第2行开始（跳过标题）
        cell_value = sheet2.cell(row=row_idx, column=match_col2_idx).value
        if cell_value is not None:
            match_values_map[str(cell_value)] = row_idx
    
    # 合并数据
    for row_idx1 in range(2, sheet1.max_row + 1):  # 从第2行开始（跳过标题）
        match_value = sheet1.cell(row=row_idx1, column=match_col1_idx).value
        
        if match_value is not None and str(match_value) in match_values_map:
            row_idx2 = match_values_map[str(match_value)]
            
            # 复制表格2中的数据到表格1
            for col_name, col_idx2 in zip(add_cols, add_cols_idx):
                col_idx1 = headers1.index(col_name) + 1
                sheet1.cell(row=row_idx1, column=col_idx1).value = sheet2.cell(row=row_idx2, column=col_idx2).value
    
    print(f"合并后的表格大小: {sheet1.max_row} 行 x {sheet1.max_column} 列")
    
    # 选择保存位置
    print("请选择保存合并结果的位置...")
    save_path = filedialog.asksaveasfilename(defaultextension=".xlsx", filetypes=[("Excel files", "*.xlsx")])
    
    if not save_path:
        print("未选择保存位置，程序退出。")
        exit()
    
    # 保存结果
    print(f"正在保存结果到 {save_path}...")
    workbook.save(save_path)
    
    if os.path.exists(save_path):
        print(f"合并完成！结果已保存至: {save_path}")
        print(f"文件大小: {os.path.getsize(save_path)} 字节")
        messagebox.showinfo("合并完成", f"合并完成！结果已保存至:\n{save_path}")
    else:
        print("保存失败，文件未创建。")

except Exception as e:
    print(f"发生错误: {str(e)}")
    messagebox.showerror("错误", f"发生错误: {str(e)}")

finally:
    input("按回车键退出...")