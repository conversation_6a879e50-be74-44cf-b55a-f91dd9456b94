"""
电报消息JSON转Excel工具

这个脚本用于将电报消息的JSON格式转换为Excel表格。
它可以提取消息中的各种字段，如代币地址、名称、符号等，
并将它们保存为Excel表格。

使用方法：
1. 直接运行脚本，会弹出文件选择对话框，选择JSON文件
2. 使用 --test 参数运行脚本，会使用测试文件 test_message.json
"""

import json
import pandas as pd
import tkinter as tk
from tkinter import filedialog
import re

def extract_text_from_json(text_array):
    """从JSON文本数组中提取完整文本"""
    full_text = ""
    for item in text_array:
        if isinstance(item, str):
            full_text += item
        elif isinstance(item, dict) and "text" in item:
            full_text += item["text"]
    return full_text

def process_field_value(field_name, value, special_rules):
    """根据特殊规则处理字段值"""
    if not value:
        return None

    # 处理值为null的情况
    if value.lower() == "null" or value.strip() == "":
        return None

    # 移除可能的表情符号
    value = re.sub(r'[\U00010000-\U0010ffff\u2600-\u26FF\u2700-\u27BF]', '', value).strip()

    # 特殊处理网站和Telegram字段
    if field_name in ["网站", "Telegram"]:
        if value.lower() == "null" or value.strip() == "":
            return None
        # 检查是否包含有效的URL或文本内容
        if len(value.strip()) < 3:  # 太短的内容可能是错误提取的
            return None

    if field_name in special_rules:
        rule = special_rules[field_name]
        if rule == "only_number":
            # 只提取数字部分
            match = re.search(r'(\d+(?:\.\d+)?)', value)
            return match.group(1) if match else value
        elif rule == "before_percent":
            # 提取百分号前的数字
            match = re.search(r'(\d+(?:\.\d+)?)\s*%', value)
            return match.group(1) if match else value
        elif rule == "with_sign_before_percent":
            # 提取百分号前带加减号的数字
            match = re.search(r'([+-]\d+(?:\.\d+)?)\s*%', value)
            return match.group(1) if match else value
        elif rule == "full_text":
            # 保留完整文本
            return value
        elif rule == "remove_backticks":
            # 移除反引号
            return value.strip('`')
        elif rule == "description_check":
            # 描述字段特殊处理
            # 如果值为空、null或者只有空白字符，返回"0"
            if not value or value.lower() == "null" or value.strip() == "":
                return "0"
            # 如果值的长度大于等于7个字符，返回"1"
            elif len(value.strip()) >= 7:
                return "1"
            # 其他情况（值存在但长度小于7个字符），返回"0"
            else:
                return "0"
        elif rule == "before_slash_after_bracket":
            # 提取(45/45)中的45
            # 尝试多种模式匹配
            patterns = [
                r'\((\d+)/\d+\)',  # 匹配(45/45)
                r'\((\d+)\/\d+\)',  # 匹配(45/45)，使用转义的斜杠
                r'(\d+)/\d+',  # 匹配45/45（没有括号）
                r'(\d+)\/\d+'  # 匹配45/45（没有括号），使用转义的斜杠
            ]

            for pattern in patterns:
                match = re.search(pattern, value)
                if match:
                    return match.group(1)

            # 如果所有模式都不匹配，返回原始值
            return value
        elif rule == "before_slash":
            # 提取2/8中的2
            match = re.search(r'(\d+)\/\d+', value)
            return match.group(1) if match else value
        elif rule == "minutes_only":
            # 提取"239分钟. 2025-04-27 20:56:19"中的"239"
            match = re.search(r'(\d+)分钟', value)
            return match.group(1) if match else value
        elif rule == "rug_pull_format":
            # 提取Rug Pull统计格式: "3/4 /100.0/" -> "3/4"
            match = re.search(r'(\d+/\d+)\s*/[\d.]+/', value)
            return match.group(1) if match else value

    return value

def parse_telegram_message(text_array):
    """解析电报消息的JSON格式，提取字段和值"""
    # 提取所有字段
    extracted_data = {}

    # 检查text_array的结构，如果是纯字符串数组，则合并为单个字符串处理
    if text_array and all(isinstance(item, str) for item in text_array):
        # 纯字符串数组，合并为单个字符串
        full_text = "".join(text_array)
        # 将合并后的字符串重新放入数组，以便后续处理逻辑可以正常工作
        text_array = [full_text]

    # 构建完整文本，用于后续查找
    full_text = ""
    for item in text_array:
        if isinstance(item, str):
            full_text += item
        elif isinstance(item, dict) and "text" in item:
            full_text += item["text"]

    # 由于实际数据中text_array可能是字符串数组，我们主要使用full_text进行解析
    # 但保留原有的数组遍历逻辑以防某些数据格式不同

    # 由于数据是纯字符串，直接从full_text中提取所有字段
    # 先处理特殊的多值字段（老鼠仓）
    mouse_cage_values = []
    mouse_cage_pattern = r'🐀[^:]*?老鼠仓[^:]*?:\s*([^\n]+)'
    mouse_cage_matches = re.finditer(mouse_cage_pattern, full_text)
    for match in mouse_cage_matches:
        value_part = match.group(1).strip()
        # 处理值为null的情况
        if value_part.lower() == "null" or value_part.lower() == "null🐀":
            value_part = "null"
        else:
            # 提取数字部分
            number_match = re.search(r'(\d+)', value_part)
            if number_match:
                value_part = number_match.group(1)
            # 移除可能的表情符号
            value_part = re.sub(r'[\U00010000-\U0010ffff\u2600-\u26FF\u2700-\u27BF]', '', value_part).strip()
        mouse_cage_values.append(value_part)

    # 处理Pro Traders字段的特殊情况
    pro_traders_value = None
    pro_traders_patterns = [
        r'👨‍💼[^:]*?Pro\s*Traders[^:]*?:\s*([^\n]+)',
        r'Pro\s*Traders[^:]*?:\s*([^\n]+)',
        r'👨‍💼[^:]*?:\s*(\d+)'
    ]
    for pattern in pro_traders_patterns:
        pro_match = re.search(pattern, full_text)
        if pro_match:
            pro_traders_value = pro_match.group(1).strip()
            break

    # 定义需要提取的字段和特殊处理规则
    fields_to_extract = {
        # === 基础信息 ===
        "代币地址": "remove_backticks",
        "名称": "full_text",
        "符号": "full_text",
        "描述": "description_check",
        "创建者": "remove_backticks",
        "创建时间": "minutes_only",  # 只提取分钟前的数值
        "网站": "full_text",
        "Twitter": "full_text",
        "Telegram": "full_text",
        "DexPaid支付时间": "minutes_only",
        "检测时间": "full_text",

        # === SolanaTracker风险数据 ===
        "狙击数": "only_number",
        "狙击占比": "before_percent",
        "内幕数": "only_number",
        "内幕占比": "before_percent",
        "top 10占比": "before_percent",
        "holder人数": "only_number",
        "1m Change": "with_sign_before_percent",
        "5m Change": "with_sign_before_percent",

        # === Axiom指标 ===
        "开发者代币总数": "only_number",
        "已迁移代币数": "only_number",
        "持有者数量": "only_number",
        "机器人用户数": "only_number",
        "前10大持有者占比": "before_percent",
        "开发者持有占比": "before_percent",
        "内部人士持有占比": "before_percent",
        "打包者持有占比": "before_percent",
        "狙击手持有占比": "before_percent",
        "总配对费用": "only_number",

        # === Ave.ai标签统计 ===
        "Cabal": "only_number",  # 阴谋集团
        "Phishing": "only_number",  # 钓鱼地址
        "Insiders": "only_number",  # 老鼠仓
        "Bundle": "only_number",  # 捆绑地址
        "Snipers": "only_number",  # 狙击
        "DEV": "only_number",
        "Whale": "only_number",  # 巨鲸
        "Smarters": "only_number",  # 聪明钱
        "KOL": "only_number",

        # === Rug Pull统计 ===
        "Rug Pull统计": "rug_pull_format",  # 特殊格式: 3/4 /100.0/
        "Insiders_RUG": "only_number",  # Rug Pull中的Insiders数值
        "Phishing_RUG": "only_number",  # Rug Pull中的Phishing数值
        "Cabal_RUG": "only_number",     # Rug Pull中的Cabal数值
        "Bundle_RUG": "only_number",    # Rug Pull中的Bundle数值
        "All Tag Rate": "only_number",

        # === GMGN统计数据 ===
        "蓝筹持有者占比": "only_number",
        "信号数量": "only_number",
        "Degen呼叫数": "only_number",
        "顶级老鼠交易者占比": "only_number",
        "顶级打包者占比": "only_number",
        "顶级陷阱交易者占比": "only_number",
        "平均持有余额": "only_number",
        "前10持有者占比": "only_number",
        "前100持有者占比": "only_number",

        # === 智能钱包统计 ===
        "智能钱包": "only_number",
        "新钱包": "only_number",
        "知名钱包": "only_number",
        "创建者钱包": "only_number",
        "狙击手钱包": "only_number",
        "老鼠交易者钱包": "only_number",
        "鲸鱼钱包": "only_number",
        "顶级钱包": "only_number",
        "关注钱包": "only_number",
        "打包者钱包": "only_number",

        # === Twitter和交易数据 ===
        "Twitter变更次数": "only_number",
        "Twitter删除帖子代币数": "only_number",
        "Twitter创建代币数": "only_number",
        "创建者代币余额": "only_number",
        "DEX广告状态": "only_number",
        "CTO标志": "only_number",
        "当前价格": "only_number",
        "1分钟价格": "only_number",
        "1分钟买入次数": "only_number",
        "1分钟卖出次数": "only_number",
        "1分钟交易量": "only_number",
        "1分钟买入量": "only_number",
        "1分钟卖出量": "only_number",
        "5分钟买入次数": "only_number",
        "5分钟卖出次数": "only_number",
        "5分钟交易量": "only_number",
        "5分钟买入量": "only_number",
        "5分钟卖出量": "only_number",
        "热度等级": "only_number",

        # === 钱包分析数据 ===
        "hold": "only_number",
        "bought_more": "only_number",
        "transfered": "only_number",
        "bought_rate": "only_number",
        "holding_rate": "only_number",
        "符合条件的钱包数量": "only_number",
        "最大持有者占比": "only_number"
    }

    # 忽略的字段（不需要提取的字段）
    ignore_fields = [
        "虚拟代币储备",
        "DexPaid状态",
        "跑路概率",
        "新代币检测",
        "Axiom指标",
        "BullX指标",
        "GMGN指标",
        "相关消息数量",
        "Telegram频道信息",
        "猎人频道信息"
    ]

    # 提取所有字段已在函数开始部分定义

    # 跳过原有的数组遍历逻辑，直接使用正则表达式从full_text中提取所有字段
    # 这样可以处理纯字符串数组的情况

    # 使用正则表达式从完整文本中提取字段和值
    # 修改正则表达式，更精确地匹配字段和值，避免空值时匹配到下一行
    pattern = r'([^\n:]+):\s*([^\n]*?)(?=\n|$)'
    matches = re.finditer(pattern, full_text)

    # 特别关注的字段 - 包含所有需要提取的字段
    special_fields = [
        # === 基础信息 ===
        "代币地址", "名称", "符号", "描述", "创建者", "创建时间",
        "网站", "Twitter", "Telegram", "DexPaid支付时间", "检测时间",

        # === SolanaTracker风险数据 ===
        "狙击数", "狙击占比", "内幕数", "内幕占比", "top 10占比", "holder人数",
        "1m Change", "5m Change",

        # === Axiom指标 ===
        "开发者代币总数", "已迁移代币数", "持有者数量", "机器人用户数", "前10大持有者占比",
        "开发者持有占比", "内部人士持有占比", "打包者持有占比", "狙击手持有占比", "总配对费用",

        # === Ave.ai标签统计 ===
        "Cabal", "Phishing", "Insiders", "Bundle", "Snipers", "DEV", "Whale", "Smarters", "KOL",

        # === Rug Pull统计 ===
        "Rug Pull统计", "Insiders_RUG", "Phishing_RUG", "Cabal_RUG", "Bundle_RUG", "All Tag Rate",

        # === GMGN统计数据 ===
        "蓝筹持有者占比", "信号数量", "Degen呼叫数", "顶级老鼠交易者占比", "顶级打包者占比",
        "顶级陷阱交易者占比", "平均持有余额", "前10持有者占比", "前100持有者占比",

        # === 智能钱包统计 ===
        "智能钱包", "新钱包", "知名钱包", "创建者钱包", "狙击手钱包", "老鼠交易者钱包",
        "鲸鱼钱包", "顶级钱包", "关注钱包", "打包者钱包",

        # === Twitter和交易数据 ===
        "Twitter变更次数", "Twitter删除帖子代币数", "Twitter创建代币数", "创建者代币余额",
        "DEX广告状态", "CTO标志", "当前价格", "1分钟价格", "1分钟买入次数", "1分钟卖出次数",
        "1分钟交易量", "1分钟买入量", "1分钟卖出量", "5分钟买入次数", "5分钟卖出次数",
        "5分钟交易量", "5分钟买入量", "5分钟卖出量", "热度等级",

        # === 钱包分析数据 ===
        "hold", "bought_more", "transfered", "bought_rate", "holding_rate",
        "符合条件的钱包数量", "最大持有者占比"
    ]

    for match in matches:
        field = match.group(1).strip()
        # 移除字段前的表情符号
        field = re.sub(r'^[\U00010000-\U0010ffff\u2600-\u26FF\u2700-\u27BF]+\s*', '', field)

        # 检查是否是需要提取的字段
        if field in fields_to_extract and field not in ignore_fields and field not in extracted_data:
            value = match.group(2).strip()

            # 特殊处理网站和Telegram字段
            if field in ["网站", "Telegram"]:
                # 如果值为null或空，设置为None
                if value.lower() == "null" or value.strip() == "":
                    extracted_data[field] = None
                # 检查值是否包含有效的URL
                elif "http" in value or "www" in value:
                    processed_value = process_field_value(field, value, fields_to_extract)
                    extracted_data[field] = processed_value
                # 如果值看起来不像URL，可能是错误提取的内容
                else:
                    extracted_data[field] = None
            else:
                processed_value = process_field_value(field, value, fields_to_extract)
                extracted_data[field] = processed_value

    # 如果之前找到了Pro Traders字段，直接添加到提取的数据中
    if pro_traders_value is not None:
        processed_value = process_field_value("Pro Traders", pro_traders_value, fields_to_extract)
        extracted_data["Pro Traders"] = processed_value

    # 处理找到的老鼠仓字段
    if len(mouse_cage_values) >= 1:
        # 第一个老鼠仓值
        value = mouse_cage_values[0]
        if value.lower() == "null":
            extracted_data["老鼠仓"] = None
        else:
            processed_value = process_field_value("老鼠仓", value, fields_to_extract)
            extracted_data["老鼠仓"] = processed_value

        # 如果有第二个老鼠仓值，添加为老鼠仓2
        if len(mouse_cage_values) >= 2:
            value = mouse_cage_values[1]
            if value.lower() == "null":
                extracted_data["老鼠仓2"] = None
            else:
                processed_value = process_field_value("老鼠仓", value, fields_to_extract)
                extracted_data["老鼠仓2"] = processed_value

    # === 新增字段的特殊处理 ===

    # 处理Rug Pull统计的特殊格式
    rug_pull_pattern = r'Rug Pull统计:\s*(\d+/\d+)\s*/([0-9.]+)/'
    rug_pull_match = re.search(rug_pull_pattern, full_text)
    if rug_pull_match:
        extracted_data["Rug Pull统计"] = rug_pull_match.group(1)  # 例如: "3/4"

    # 处理Rug Pull统计下的各项数值
    rug_insiders_pattern = r'Insiders:\s*([0-9.]+)'
    rug_insiders_match = re.search(rug_insiders_pattern, full_text)
    if rug_insiders_match:
        extracted_data["Insiders_RUG"] = rug_insiders_match.group(1)

    rug_phishing_pattern = r'Phishing:\s*([0-9.]+)'
    rug_phishing_match = re.search(rug_phishing_pattern, full_text)
    if rug_phishing_match:
        extracted_data["Phishing_RUG"] = rug_phishing_match.group(1)

    rug_cabal_pattern = r'Cabal:\s*([0-9.]+)'
    rug_cabal_match = re.search(rug_cabal_pattern, full_text)
    if rug_cabal_match:
        extracted_data["Cabal_RUG"] = rug_cabal_match.group(1)

    rug_bundle_pattern = r'Bundle:\s*([0-9.]+)'
    rug_bundle_match = re.search(rug_bundle_pattern, full_text)
    if rug_bundle_match:
        extracted_data["Bundle_RUG"] = rug_bundle_match.group(1)

    # 处理Ave.ai标签统计中的特殊格式 (例如: "Phishing /钓鱼地址: 6")
    ave_ai_patterns = [
        (r'Cabal\s*/阴谋集团:\s*(\w+)', "Cabal"),
        (r'Phishing\s*/钓鱼地址:\s*(\w+)', "Phishing"),
        (r'Insiders\s*/老鼠仓:\s*(\w+)', "Insiders"),
        (r'Bundle\s*/捆绑地址:\s*(\w+)', "Bundle"),
        (r'Snipers\s*/狙击:\s*(\w+)', "Snipers"),
        (r'DEV\s*/DEV:\s*(\w+)', "DEV"),
        (r'Whale\s*/巨鲸:\s*(\w+)', "Whale"),
        (r'Smarters\s*/聪明钱:\s*(\w+)', "Smarters"),
        (r'KOL\s*/KOL:\s*(\w+)', "KOL")
    ]

    for pattern, field_name in ave_ai_patterns:
        match = re.search(pattern, full_text)
        if match:
            value = match.group(1)
            if value.lower() == "null":
                extracted_data[field_name] = None
            else:
                extracted_data[field_name] = value

    # 检查特别关注的字段是否已提取
    for field in special_fields:
        if field not in extracted_data:
            # 尝试使用更宽松的模式再次查找
            loose_pattern = rf'{field}[^\n]*?:\s*(.*?)(?:\n|$)'
            loose_match = re.search(loose_pattern, full_text, re.IGNORECASE)
            if loose_match:
                value = loose_match.group(1).strip()

                # 特殊处理网站和Telegram字段
                if field in ["网站", "Telegram"]:
                    # 如果值为null或空，设置为None
                    if value.lower() == "null" or value.strip() == "":
                        extracted_data[field] = None
                    # 检查值是否包含有效的URL
                    elif "http" in value or "www" in value:
                        processed_value = process_field_value(field, value, fields_to_extract)
                        extracted_data[field] = processed_value
                    # 如果值看起来不像URL，可能是错误提取的内容
                    else:
                        extracted_data[field] = None
                else:
                    processed_value = process_field_value(field, value, fields_to_extract)
                    extracted_data[field] = processed_value
            # 对于Insiders字段，尝试使用更特殊的模式
            elif field == "Insiders":
                # 尝试多种模式匹配
                patterns = [
                    r'🔐\s*\*\*Insiders\*\*:\s*([^\n]+)',  # 匹配 🔐 **Insiders**: 值
                    r'🔐[^:]*?Insiders[^:]*?:\s*([^\n]+)',  # 匹配表情符号后面的Insiders
                    r'\*\*Insiders\*\*:\s*([^\n]+)',  # 匹配 **Insiders**: 值
                    r'Insiders[^:]*?:\s*([^\n]+)',  # 直接匹配Insiders: 值
                ]

                for pattern in patterns:
                    insiders_match = re.search(pattern, full_text, re.IGNORECASE)
                    if insiders_match:
                        value = insiders_match.group(1).strip()
                        processed_value = process_field_value(field, value, fields_to_extract)
                        extracted_data[field] = processed_value
                        break
            # 对于Pro Traders字段，尝试使用更特殊的模式
            elif field == "Pro Traders":
                # 尝试多种模式匹配
                patterns = [
                    r'👨‍💼[^:]*?:\s*(\d+)',  # 匹配表情符号后面的数字
                    r'Pro\s*Traders\s*:\s*(\d+)',  # 直接匹配Pro Traders: 数字
                    r'Pro\s*Trader[^:]*?:\s*(\d+)',  # 匹配Pro Trader后面的数字
                    r'Traders\s*:\s*(\d+)'  # 匹配Traders: 数字
                ]

                for pattern in patterns:
                    pro_match = re.search(pattern, full_text, re.IGNORECASE)
                    if pro_match:
                        value = pro_match.group(1).strip()
                        processed_value = process_field_value(field, value, fields_to_extract)
                        extracted_data[field] = processed_value
                        break

                # 如果还是没找到，设置一个默认值
                if field not in extracted_data:
                    extracted_data[field] = 0
            # 对于开发者(DEV)字段，尝试使用更特殊的模式
            elif field == "开发者(DEV)":
                # 尝试多种模式匹配
                patterns = [
                    r'开发者\(DEV\)\s*:\s*(\d+)',  # 直接匹配开发者(DEV): 数字
                    r'开发者.*?\(DEV\)\s*:\s*(\d+)',  # 匹配开发者xxx(DEV): 数字
                    r'DEV\s*:\s*(\d+)'  # 匹配DEV: 数字
                ]

                for pattern in patterns:
                    dev_match = re.search(pattern, full_text, re.IGNORECASE)
                    if dev_match:
                        value = dev_match.group(1).strip()
                        processed_value = process_field_value(field, value, fields_to_extract)
                        extracted_data[field] = processed_value
                        break

                # 如果还是没找到，尝试使用开发者字段的值
                if field not in extracted_data and "开发者" in extracted_data:
                    extracted_data[field] = extracted_data["开发者"]
            # 对于网站和Telegram字段，确保它们存在且值为None
            elif field in ["网站", "Telegram"]:
                extracted_data[field] = None
            # 对于描述字段，确保它存在且值为"0"（表示没有描述或描述太短）
            elif field == "描述":
                extracted_data[field] = "0"
            # 对于创建时间、山丘之王时间戳和DexPaid支付时间字段，尝试使用更特殊的模式
            elif field in ["创建时间", "山丘之王时间戳", "DexPaid支付时间"]:
                # 尝试多种模式匹配
                patterns = [
                    r'(\d+)分钟',  # 匹配"3分钟"
                    r'(\d+)\s*分钟',  # 匹配"3 分钟"
                    r'(\d+)分'  # 匹配"3分"
                ]

                for pattern in patterns:
                    # 根据字段名构建更精确的正则表达式
                    field_pattern = rf'{field}[^\n]*?:\s*{pattern}'
                    time_match = re.search(field_pattern, full_text, re.IGNORECASE)
                    if time_match:
                        value = time_match.group(1).strip()
                        extracted_data[field] = value
                        break

                # 如果还是没找到，设置一个默认值
                if field not in extracted_data:
                    extracted_data[field] = 0

    return extracted_data

def process_json_file():
    """处理JSON文件并转换为Excel"""
    # 创建文件选择对话框
    root = tk.Tk()
    root.withdraw()

    # 选择JSON文件
    json_file_path = filedialog.askopenfilename(
        title="选择JSON文件",
        filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
    )

    if not json_file_path:
        print("未选择文件，操作取消")
        return

    try:
        # 读取JSON文件
        with open(json_file_path, 'r', encoding='utf-8') as file:
            json_content = file.read()

        # 尝试解析JSON
        try:
            data = json.loads(json_content)
        except json.JSONDecodeError:
            # 尝试修复JSON格式
            fixed_content = json_content.strip()
            try:
                data = json.loads(fixed_content)
            except json.JSONDecodeError:
                print("无法解析JSON文件，请检查文件格式")
                return

        # 存储所有提取的数据
        all_extracted_data = []

        # 检查不同的JSON结构
        if isinstance(data, dict):
            # 检查是否有messages数组
            if "messages" in data and isinstance(data["messages"], list) and len(data["messages"]) > 0:
                messages = data["messages"]
                # 处理所有消息
                processed_count = 0
                for message in messages:
                    if "text" in message and isinstance(message["text"], list):
                        extracted_data = parse_telegram_message(message["text"])

                        # 确保Pro Traders字段存在
                        if "Pro Traders" not in extracted_data:
                            # 尝试在原始文本中查找
                            text_str = ""
                            for item in message["text"]:
                                if isinstance(item, str):
                                    text_str += item
                                elif isinstance(item, dict) and "text" in item:
                                    text_str += item["text"]

                            # 尝试匹配Pro Traders
                            pro_traders_match = re.search(r'Pro\s*Traders\s*:\s*(\d+)', text_str, re.IGNORECASE)
                            if pro_traders_match:
                                extracted_data["Pro Traders"] = pro_traders_match.group(1)
                            else:
                                # 设置默认值
                                extracted_data["Pro Traders"] = 0

                        if extracted_data and len(extracted_data) > 0:
                            all_extracted_data.append(extracted_data)
                            processed_count += 1

            # 直接检查是否有text字段
            elif "text" in data and isinstance(data["text"], list):
                extracted_data = parse_telegram_message(data["text"])

                # 确保Pro Traders字段存在
                if "Pro Traders" not in extracted_data:
                    # 尝试在原始文本中查找
                    text_str = ""
                    for item in data["text"]:
                        if isinstance(item, str):
                            text_str += item
                        elif isinstance(item, dict) and "text" in item:
                            text_str += item["text"]

                    # 尝试匹配Pro Traders
                    pro_traders_match = re.search(r'Pro\s*Traders\s*:\s*(\d+)', text_str, re.IGNORECASE)
                    if pro_traders_match:
                        extracted_data["Pro Traders"] = pro_traders_match.group(1)
                    else:
                        # 设置默认值
                        extracted_data["Pro Traders"] = 0

                if extracted_data:
                    all_extracted_data.append(extracted_data)
        else:
            print("未找到有效的消息数据结构")
            return

        if not all_extracted_data:
            print("未找到有效的消息数据")
            return

        # 创建DataFrame
        df = pd.DataFrame(all_extracted_data)

        # 确保特定列存在
        required_columns = [
            "Pro Traders", "Top50状态", "24H换手率", "聪明钱", "开发者(DEV)",
            "老鼠仓", "老鼠仓2", "钓鱼地址", "阴谋集团", "巨鲸",
            "Buys", "Sells", "DexPaid支付时间", "跑路比例", "1m涨跌幅"  # 添加1m涨跌幅到必需列
        ]

        for col in required_columns:
            if col not in df.columns:
                df[col] = None

        # 定义有效的列名列表
        valid_columns = [
            # === 基础信息 ===
            "代币地址", "名称", "符号", "描述", "创建者", "创建时间",
            "网站", "Twitter", "Telegram", "DexPaid支付时间", "检测时间",

            # === SolanaTracker风险数据 ===
            "狙击数", "狙击占比", "内幕数", "内幕占比", "top 10占比", "holder人数",
            "1m Change", "5m Change",

            # === Axiom指标 ===
            "开发者代币总数", "已迁移代币数", "持有者数量", "机器人用户数", "前10大持有者占比",
            "开发者持有占比", "内部人士持有占比", "打包者持有占比", "狙击手持有占比", "总配对费用",

            # === Ave.ai标签统计 ===
            "Cabal", "Phishing", "Insiders", "Bundle", "Snipers", "DEV", "Whale", "Smarters", "KOL",

            # === Rug Pull统计 ===
            "Rug Pull统计", "Insiders_RUG", "Phishing_RUG", "Cabal_RUG", "Bundle_RUG", "All Tag Rate",

            # === GMGN统计数据 ===
            "蓝筹持有者占比", "信号数量", "Degen呼叫数", "顶级老鼠交易者占比", "顶级打包者占比",
            "顶级陷阱交易者占比", "平均持有余额", "前10持有者占比", "前100持有者占比",

            # === 智能钱包统计 ===
            "智能钱包", "新钱包", "知名钱包", "创建者钱包", "狙击手钱包", "老鼠交易者钱包",
            "鲸鱼钱包", "顶级钱包", "关注钱包", "打包者钱包",

            # === Twitter和交易数据 ===
            "Twitter变更次数", "Twitter删除帖子代币数", "Twitter创建代币数", "创建者代币余额",
            "DEX广告状态", "CTO标志", "当前价格", "1分钟价格", "1分钟买入次数", "1分钟卖出次数",
            "1分钟交易量", "1分钟买入量", "1分钟卖出量", "5分钟买入次数", "5分钟卖出次数",
            "5分钟交易量", "5分钟买入量", "5分钟卖出量", "热度等级",

            # === 钱包分析数据 ===
            "hold", "bought_more", "transfered", "bought_rate", "holding_rate",
            "符合条件的钱包数量", "最大持有者占比"
        ]

        # 只保留有效的列
        df = df[[col for col in valid_columns if col in df.columns]]

        # 选择保存Excel文件的路径
        excel_file_path = filedialog.asksaveasfilename(
            title="保存Excel文件",
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )

        if not excel_file_path:
            print("未选择保存路径，操作取消")
            return

        # 保存为Excel
        df.to_excel(excel_file_path, index=False)
        print(f"数据已成功保存到: {excel_file_path}，共 {len(all_extracted_data)} 条记录")

    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")

def test_with_file(file_path):
    """使用指定的文件进行测试"""
    try:
        # 读取JSON文件
        with open(file_path, 'r', encoding='utf-8') as file:
            json_content = file.read()

        # 尝试解析JSON
        try:
            data = json.loads(json_content)
        except json.JSONDecodeError:
            # 尝试修复JSON格式
            fixed_content = json_content.strip()
            try:
                data = json.loads(fixed_content)
            except json.JSONDecodeError:
                print("无法解析JSON文件，请检查文件格式")
                return

        # 存储所有提取的数据
        all_extracted_data = []

        # 检查不同的JSON结构
        if isinstance(data, dict):
            # 检查是否有messages数组
            if "messages" in data and isinstance(data["messages"], list) and len(data["messages"]) > 0:
                messages = data["messages"]
                # 处理所有消息
                processed_count = 0
                for message in messages:
                    if "text" in message and isinstance(message["text"], list):
                        extracted_data = parse_telegram_message(message["text"])

                        # 确保Pro Traders字段存在
                        if "Pro Traders" not in extracted_data:
                            # 尝试在原始文本中查找
                            text_str = ""
                            for item in message["text"]:
                                if isinstance(item, str):
                                    text_str += item
                                elif isinstance(item, dict) and "text" in item:
                                    text_str += item["text"]

                            # 尝试匹配Pro Traders
                            pro_traders_match = re.search(r'Pro\s*Traders\s*:\s*(\d+)', text_str, re.IGNORECASE)
                            if pro_traders_match:
                                extracted_data["Pro Traders"] = pro_traders_match.group(1)
                            else:
                                # 设置默认值
                                extracted_data["Pro Traders"] = 0

                        if extracted_data and len(extracted_data) > 0:
                            all_extracted_data.append(extracted_data)
                            processed_count += 1

            # 直接检查是否有text字段
            elif "text" in data and isinstance(data["text"], list):
                extracted_data = parse_telegram_message(data["text"])

                # 确保Pro Traders字段存在
                if "Pro Traders" not in extracted_data:
                    # 尝试在原始文本中查找
                    text_str = ""
                    for item in data["text"]:
                        if isinstance(item, str):
                            text_str += item
                        elif isinstance(item, dict) and "text" in item:
                            text_str += item["text"]

                    # 尝试匹配Pro Traders
                    pro_traders_match = re.search(r'Pro\s*Traders\s*:\s*(\d+)', text_str, re.IGNORECASE)
                    if pro_traders_match:
                        extracted_data["Pro Traders"] = pro_traders_match.group(1)
                    else:
                        # 设置默认值
                        extracted_data["Pro Traders"] = 0

                if extracted_data:
                    all_extracted_data.append(extracted_data)
        else:
            print("未找到有效的消息数据结构")
            return

        if not all_extracted_data:
            print("未找到有效的消息数据")
            return

        # 创建DataFrame
        df = pd.DataFrame(all_extracted_data)

        # 确保特定列存在
        required_columns = [
            "Pro Traders", "Top50状态", "24H换手率", "聪明钱", "开发者(DEV)",
            "老鼠仓", "老鼠仓2", "钓鱼地址", "阴谋集团", "巨鲸",
            "Buys", "Sells", "DexPaid支付时间", "跑路比例", "1m涨跌幅"  # 添加1m涨跌幅到必需列
        ]

        for col in required_columns:
            if col not in df.columns:
                df[col] = None

        # 定义有效的列名列表
        valid_columns = [
            # === 基础信息 ===
            "代币地址", "名称", "符号", "描述", "创建者", "创建时间",
            "网站", "Twitter", "Telegram", "DexPaid支付时间", "检测时间",

            # === SolanaTracker风险数据 ===
            "狙击数", "狙击占比", "内幕数", "内幕占比", "top 10占比", "holder人数",
            "1m Change", "5m Change",

            # === Axiom指标 ===
            "开发者代币总数", "已迁移代币数", "持有者数量", "机器人用户数", "前10大持有者占比",
            "开发者持有占比", "内部人士持有占比", "打包者持有占比", "狙击手持有占比", "总配对费用",

            # === Ave.ai标签统计 ===
            "Cabal", "Phishing", "Insiders", "Bundle", "Snipers", "DEV", "Whale", "Smarters", "KOL",

            # === Rug Pull统计 ===
            "Rug Pull统计", "Insiders_RUG", "Phishing_RUG", "Cabal_RUG", "Bundle_RUG", "All Tag Rate",

            # === GMGN统计数据 ===
            "蓝筹持有者占比", "信号数量", "Degen呼叫数", "顶级老鼠交易者占比", "顶级打包者占比",
            "顶级陷阱交易者占比", "平均持有余额", "前10持有者占比", "前100持有者占比",

            # === 智能钱包统计 ===
            "智能钱包", "新钱包", "知名钱包", "创建者钱包", "狙击手钱包", "老鼠交易者钱包",
            "鲸鱼钱包", "顶级钱包", "关注钱包", "打包者钱包",

            # === Twitter和交易数据 ===
            "Twitter变更次数", "Twitter删除帖子代币数", "Twitter创建代币数", "创建者代币余额",
            "DEX广告状态", "CTO标志", "当前价格", "1分钟价格", "1分钟买入次数", "1分钟卖出次数",
            "1分钟交易量", "1分钟买入量", "1分钟卖出量", "5分钟买入次数", "5分钟卖出次数",
            "5分钟交易量", "5分钟买入量", "5分钟卖出量", "热度等级",

            # === 钱包分析数据 ===
            "hold", "bought_more", "transfered", "bought_rate", "holding_rate",
            "符合条件的钱包数量", "最大持有者占比"
        ]

        # 只保留有效的列
        df = df[[col for col in valid_columns if col in df.columns]]

        # 保存为Excel
        excel_file_path = file_path.replace('.json', '.xlsx')
        df.to_excel(excel_file_path, index=False)
        print(f"数据已成功保存到: {excel_file_path}，共 {len(all_extracted_data)} 条记录")

    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")

if __name__ == "__main__":
    import sys

    # 检查是否有命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        # 测试模式，使用测试文件
        test_file = "test_message.json"
        print(f"测试模式，使用文件: {test_file}")
        test_with_file(test_file)
    else:
        # 正常模式，使用文件选择对话框
        process_json_file()
