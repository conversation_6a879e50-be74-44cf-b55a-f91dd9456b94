

import os
from datetime import datetime

class Config:
    """配置类"""

    # Dune API 配置
    DUNE_API_KEY = 'aoIRZzGSZaHhJy8gTkHDeDpDYSMUQcDs'  # 请替换为您的实际API密钥
    DUNE_QUERY_ID = 4974281  # 请替换为您的实际查询ID
    DUNE_REQUEST_TIMEOUT = 300  # API请求超时时间（秒）

    # 文件路径配置
    DATA_DIR = "./data"  # 数据目录
    OUTPUT_DIR = "./output"  # 输出目录

    # 默认文件名
    DEFAULT_FILENAMES = {
        'dune_raw': 'dune_query_result_raw',
        'dune_enhanced': 'dune_query_result_enhanced',
        'telegram_input': 'telegram_messages.json',
        'telegram_output': 'telegram_data_processed',
        'merged_output': 'merged_data_final',
        'cpw_output': 'data_with_cpw'
    }

    # 时间差计算配置
    TIME_DIFF_CONFIG = {
        'required_columns': ['ath_time', 'atl_time', 'added_time'],
        'output_columns': ['ath_time_diff_seconds', 'atl_time_diff_seconds'],
        'time_formats': [
            "%Y-%m-%d %H:%M:%S.%f %Z",  # 2025-05-05 06:04:20.000 UTC
            "%Y-%m-%d %H:%M:%S.%f",     # 2025-05-05 06:04:20.000
            "%Y-%m-%d %H:%M:%S %Z",     # 2025-05-05 06:04:20 UTC
            "%Y-%m-%d %H:%M:%S",        # 2025-05-05 06:04:20
            "%Y-%m-%d %H:%M:%S+00:00",  # 2025-05-05 14:43:54+00:00
            "%Y-%m-%d %H:%M:%S%z",      # 2025-05-05 14:43:54+0000
            "%Y-%m-%d %H:%M %Z",        # 2025-05-05 14:43 UTC
            "%Y-%m-%d %H:%M",           # 2025-05-05 14:43
            "%Y-%m-%d"                  # 2025-05-05
        ]
    }

    # 表格合并配置
    MERGE_CONFIG = {
        'dune_match_column': 'token_address',  # Dune表格中的匹配列
        'telegram_match_column': '代币地址',  # Telegram表格中的匹配列
        'auto_detect_columns': True,  # 自动检测匹配列
        'preserve_formatting': True,  # 保持格式
        'handle_duplicates': 'first',  # 处理重复值的方式
        'auto_merge_enabled': True,  # 启用自动合并
        'merge_output_prefix': 'merged_data_final'  # 合并输出文件前缀
    }

    # Telegram配置
    TELEGRAM_CONFIG = {
        'api_id': 24702520,
        'api_hash': '7b1f880deb5998f36a079cfdbd097534',
        'session_name': 'telegram_cpw_finder_session',
        'channel_ids': [
            -1001914959004,
            -1002050218130
        ],
        'cpw_threshold': 278,
        'result_limit': 7,
        'search_delay': 1.6  # 搜索间隔（秒）
    }

    # 数据处理配置
    DATA_PROCESSING_CONFIG = {
        'encoding': 'utf-8-sig',  # 文件编码
        'decimal_places': 1,  # 小数位数
        'handle_missing_values': True,  # 处理缺失值
        'validate_data': True,  # 数据验证
        'create_backup': True  # 创建备份
    }

    # 代理配置
    PROXY_CONFIG = {
        'proxy_url': 'http://127.0.0.1:7890',  # 代理地址
        'enabled': True,  # 代理开关：True=使用代理，False=直接连接
        'timeout': 30  # 代理连接超时时间（秒）
    }

    # 日志配置
    LOGGING_CONFIG = {
        'level': 'INFO',
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'file_enabled': True,
        'console_enabled': True
    }

    @classmethod
    def ensure_directories(cls):
        """确保必要的目录存在"""
        directories = [cls.DATA_DIR, cls.OUTPUT_DIR]
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"创建目录: {directory}")

    @classmethod
    def get_timestamped_filename(cls, base_name, extension='csv'):
        """
        获取带时间戳的文件名

        Args:
            base_name (str): 基础文件名
            extension (str): 文件扩展名

        Returns:
            str: 带时间戳的完整文件名
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{base_name}_{timestamp}.{extension}"

    @classmethod
    def get_file_path(cls, filename, directory=None):
        """
        获取完整文件路径

        Args:
            filename (str): 文件名
            directory (str): 目录，默认为OUTPUT_DIR

        Returns:
            str: 完整文件路径
        """
        if directory is None:
            directory = cls.OUTPUT_DIR

        cls.ensure_directories()
        return os.path.join(directory, filename)

    @classmethod
    def get_dune_output_path(cls, enhanced=True):
        """
        获取Dune输出文件路径

        Args:
            enhanced (bool): 是否为增强版

        Returns:
            dict: 包含CSV和Excel路径的字典
        """
        base_name = cls.DEFAULT_FILENAMES['dune_enhanced' if enhanced else 'dune_raw']

        return {
            'csv': cls.get_file_path(cls.get_timestamped_filename(base_name, 'csv')),
            'excel': cls.get_file_path(cls.get_timestamped_filename(base_name, 'xlsx'))
        }

    @classmethod
    def get_telegram_paths(cls):
        """
        获取Telegram相关文件路径

        Returns:
            dict: 包含输入和输出路径的字典
        """
        return {
            'input': cls.get_file_path(cls.DEFAULT_FILENAMES['telegram_input'], cls.DATA_DIR),
            'output_csv': cls.get_file_path(cls.get_timestamped_filename(cls.DEFAULT_FILENAMES['telegram_output'], 'csv')),
            'output_excel': cls.get_file_path(cls.get_timestamped_filename(cls.DEFAULT_FILENAMES['telegram_output'], 'xlsx'))
        }

    @classmethod
    def get_merge_paths(cls):
        """
        获取合并文件路径

        Returns:
            dict: 包含合并输出路径的字典
        """
        return {
            'output_csv': cls.get_file_path(cls.get_timestamped_filename(cls.DEFAULT_FILENAMES['merged_output'], 'csv')),
            'output_excel': cls.get_file_path(cls.get_timestamped_filename(cls.DEFAULT_FILENAMES['merged_output'], 'xlsx'))
        }

    @classmethod
    def print_config_summary(cls):
        """打印配置摘要"""
        print("📋 当前配置摘要:")
        print("=" * 50)
        print(f"Dune查询ID: {cls.DUNE_QUERY_ID}")
        print(f"数据目录: {cls.DATA_DIR}")
        print(f"输出目录: {cls.OUTPUT_DIR}")
        print(f"Telegram频道数: {len(cls.TELEGRAM_CONFIG['channel_ids'])}")
        print(f"CPW阈值: {cls.TELEGRAM_CONFIG['cpw_threshold']}")
        print(f"文件编码: {cls.DATA_PROCESSING_CONFIG['encoding']}")
        print("=" * 50)

# 创建全局配置实例
config = Config()

# 确保目录存在
config.ensure_directories()

if __name__ == "__main__":
    # 测试配置
    config.print_config_summary()

    # 测试路径生成
    print("\n📁 测试路径生成:")
    dune_paths = config.get_dune_output_path()
    print(f"Dune CSV路径: {dune_paths['csv']}")
    print(f"Dune Excel路径: {dune_paths['excel']}")

    telegram_paths = config.get_telegram_paths()
    print(f"Telegram输入路径: {telegram_paths['input']}")
    print(f"Telegram输出路径: {telegram_paths['output_csv']}")

    merge_paths = config.get_merge_paths()
    print(f"合并输出路径: {merge_paths['output_csv']}")
