#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel表格行过滤脚本
功能：根据指定关键词排除包含这些内容的数据行

"""

import pandas as pd
import os
import re
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def get_exclude_keywords():
    """
    获取排除关键词列表
    用户可以直接在这里修改排除关键词
    """
    exclude_keywords_text = """
KOLbastille1891yPzV
盈利50万美金
FWOG内盘23979ucMw
Y神
Pikapikaquqc
Pump_Fun_Alerts
Wires933
Hugozouhugog
5p1eler5
GemnlDotNet
Web3_GXFC
Ethkloppc
Ywwen15
Yihui3152
teknopeasant
lovefuma17
Tpolasxl
armenfane3
Lzz3341
Joecdawgn
TauroSignals
    """
    
    # 解析关键词列表
    keywords = []
    for line in exclude_keywords_text.strip().split('\n'):
        keyword = line.strip()
        if keyword:  # 忽略空行
            keywords.append(keyword)
    
    return keywords

def read_data_file(file_path):
    """
    读取数据文件，支持多种编码格式
    """
    print(f"正在读取文件: {file_path}")
    
    if file_path.endswith('.csv'):
        # 尝试多种编码格式读取CSV文件
        for encoding in ['utf-8', 'gbk', 'gb2312', 'latin1', 'utf-8-sig']:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                print(f"成功使用 {encoding} 编码读取CSV文件")
                return df
            except UnicodeDecodeError:
                continue
        
        # 如果所有编码都失败，使用错误处理方式
        print("警告: 使用默认编码读取文件，可能存在字符显示问题")
        df = pd.read_csv(file_path, encoding='utf-8', errors='ignore')
        return df
        
    elif file_path.endswith(('.xlsx', '.xls')):
        df = pd.read_excel(file_path)
        print("成功读取Excel文件")
        return df
    else:
        raise ValueError("不支持的文件格式，请使用 .csv、.xlsx 或 .xls 文件")

def check_row_contains_keywords(row, target_columns, keywords):
    """
    检查数据行是否包含排除关键词（精确匹配模式）
    """
    for col in target_columns:
        if col in row.index and pd.notna(row[col]):
            cell_content = str(row[col]).strip()  # 去除前后空格

            for keyword in keywords:
                keyword_stripped = keyword.strip()

                # 精确匹配逻辑：使用多种方法确保精确匹配
                if is_exact_match(cell_content, keyword_stripped):
                    return True, col, keyword

    return False, None, None

def is_exact_match(cell_content, keyword):
    """
    精确匹配函数：检查关键词是否与单元格内容精确匹配
    支持以下匹配模式：
    1. 完全相等匹配
    2. 词语边界匹配（处理多词语情况）
    3. 处理标点符号和空格
    """

    # 转换为小写进行不区分大小写比较
    cell_lower = cell_content.lower()
    keyword_lower = keyword.lower()

    # 方法1: 完全相等匹配（去除前后空格后）
    if cell_lower.strip() == keyword_lower.strip():
        return True

    # 方法2: 词语边界匹配
    # 使用正则表达式确保关键词作为独立词语存在
    # \b 表示词语边界，确保不会匹配到词语的一部分
    keyword_escaped = re.escape(keyword_lower.strip())
    pattern = r'\b' + keyword_escaped + r'\b'

    if re.search(pattern, cell_lower):
        return True

    # 方法3: 处理标点符号分隔的情况
    # 将单元格内容按常见分隔符分割，然后逐个比较
    separators = r'[,，;；|｜/\\\s\t\n\r\-_=+()（）\[\]【】{}｛｝<>《》""\'''""''`~!！@#$%^&*]+'
    cell_parts = re.split(separators, cell_lower)
    cell_parts = [part.strip() for part in cell_parts if part.strip()]

    keyword_clean = keyword_lower.strip()
    for part in cell_parts:
        if part == keyword_clean:
            return True

    return False

def filter_excel_rows(df, exclude_keywords, target_columns=['标签', '推特caller名称', 'TGCaller名称']):
    """
    过滤Excel行数据
    """
    print(f"\n开始过滤数据...")
    print(f"目标检查列: {target_columns}")
    print(f"排除关键词数量: {len(exclude_keywords)}")
    
    # 检查目标列是否存在
    existing_columns = [col for col in target_columns if col in df.columns]
    missing_columns = [col for col in target_columns if col not in df.columns]
    
    if missing_columns:
        print(f"警告: 以下列在数据中不存在: {missing_columns}")
    
    if not existing_columns:
        print("错误: 所有目标列都不存在于数据中")
        return df, [], {}
    
    print(f"实际检查列: {existing_columns}")
    
    # 记录被排除的行信息
    excluded_rows = []
    exclusion_stats = {}
    
    # 逐行检查
    rows_to_keep = []
    total_rows = len(df)
    
    for idx, row in df.iterrows():
        contains_keyword, matched_column, matched_keyword = check_row_contains_keywords(
            row, existing_columns, exclude_keywords
        )
        
        if contains_keyword:
            excluded_rows.append({
                'index': idx,
                'column': matched_column,
                'keyword': matched_keyword,
                'content': str(row[matched_column]) if matched_column in row.index else ''
            })
            
            # 统计排除原因
            if matched_keyword not in exclusion_stats:
                exclusion_stats[matched_keyword] = 0
            exclusion_stats[matched_keyword] += 1
        else:
            rows_to_keep.append(idx)
        
        # 显示进度
        if (idx + 1) % 1000 == 0 or (idx + 1) == total_rows:
            print(f"处理进度: {idx + 1}/{total_rows} ({(idx + 1)/total_rows*100:.1f}%)")
    
    # 创建过滤后的数据框
    filtered_df = df.loc[rows_to_keep].copy()
    
    return filtered_df, excluded_rows, exclusion_stats

def save_filtered_results(original_file_path, filtered_df, excluded_rows, exclusion_stats, exclude_keywords):
    """
    保存过滤结果
    """
    # 生成输出文件名
    base_name = os.path.splitext(os.path.basename(original_file_path))[0]
    timestamp = datetime.now().strftime("%m%d_%H")
    output_file = f"{base_name}_过滤后_{timestamp}.xlsx"
    
    print(f"\n正在保存结果到: {output_file}")
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 保存过滤后的主数据
        filtered_df.to_excel(writer, sheet_name='过滤后数据', index=False)
        
        # 保存排除关键词列表
        keywords_df = pd.DataFrame(exclude_keywords, columns=['排除关键词'])
        keywords_df.to_excel(writer, sheet_name='排除关键词', index=False)
        
        # 保存统计信息
        original_count = len(filtered_df) + len(excluded_rows)
        stats_info = [
            ['原始数据行数', original_count],
            ['过滤后行数', len(filtered_df)],
            ['被排除行数', len(excluded_rows)],
            ['排除比例', f"{len(excluded_rows)/original_count*100:.2f}%" if original_count > 0 else "0.00%"],
            ['保留比例', f"{len(filtered_df)/original_count*100:.2f}%" if original_count > 0 else "0.00%"],
            ['处理时间', datetime.now().strftime("%Y-%m-%d %H:%M:%S")]
        ]
        stats_df = pd.DataFrame(stats_info, columns=['统计项', '值'])
        stats_df.to_excel(writer, sheet_name='统计信息', index=False)
        
        # 保存排除详情（如果有被排除的行）
        if excluded_rows:
            exclusion_details = []
            for item in excluded_rows:
                exclusion_details.append([
                    item['index'],
                    item['column'],
                    item['keyword'],
                    item['content'][:100] + '...' if len(item['content']) > 100 else item['content']
                ])
            
            exclusion_df = pd.DataFrame(exclusion_details, 
                                      columns=['原始行号', '匹配列', '匹配关键词', '单元格内容'])
            exclusion_df.to_excel(writer, sheet_name='排除详情', index=False)
        
        # 保存排除统计
        if exclusion_stats:
            stats_list = [[keyword, count] for keyword, count in exclusion_stats.items()]
            stats_list.sort(key=lambda x: x[1], reverse=True)  # 按排除次数降序排列
            
            exclusion_stats_df = pd.DataFrame(stats_list, columns=['关键词', '排除次数'])
            exclusion_stats_df.to_excel(writer, sheet_name='关键词统计', index=False)
    
    return output_file

def main():
    """
    主函数
    """
    print("=== Excel表格行过滤器 ===\n")
    
    # 输入文件路径
    file_path = input("请输入Excel文件路径 (支持 .csv、.xlsx、.xls): ").strip().strip('"\'').strip()
    
    if not os.path.exists(file_path):
        print(f"错误: 文件 '{file_path}' 不存在")
        return
    
    try:
        # 获取排除关键词列表
        exclude_keywords = get_exclude_keywords()
        print(f"加载了 {len(exclude_keywords)} 个排除关键词")
        
        # 读取数据
        df = read_data_file(file_path)
        
        # 清理列名
        df.columns = df.columns.str.strip()
        print(f"数据加载完成: {df.shape[0]} 行, {df.shape[1]} 列")
        
        # 显示可用的列名
        print(f"\n数据列名: {list(df.columns)}")
        
        # 过滤数据
        filtered_df, excluded_rows, exclusion_stats = filter_excel_rows(df, exclude_keywords)
        
        # 显示结果统计
        original_count = len(df)
        filtered_count = len(filtered_df)
        excluded_count = len(excluded_rows)
        
        print(f"\n=== 过滤结果统计 ===")
        print(f"原始数据行数: {original_count}")
        print(f"过滤后行数: {filtered_count}")
        print(f"被排除行数: {excluded_count}")
        print(f"排除比例: {excluded_count/original_count*100:.2f}%")
        print(f"保留比例: {filtered_count/original_count*100:.2f}%")
        
        if exclusion_stats:
            print(f"\n=== 排除关键词统计 (Top 10) ===")
            sorted_stats = sorted(exclusion_stats.items(), key=lambda x: x[1], reverse=True)
            for keyword, count in sorted_stats[:10]:
                print(f"  '{keyword}': {count} 次")
        
        # 保存结果
        output_file = save_filtered_results(file_path, filtered_df, excluded_rows, exclusion_stats, exclude_keywords)
        
        print(f"\n✅ 过滤完成！结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
