#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
完全自包含的集成数据处理器
整合了Dune数据下载和Telegram消息自动下载功能

功能：
1. 从Dune API获取查询结果并计算时间差
2. 根据Dune数据时间范围自动下载Telegram消息
3. 支持代理配置
4. 统一的配置管理和错误处理
5. 完全独立，无需外部配置文件

使用方法：
1. 配置代理设置（可选）
2. 运行脚本执行完整的数据处理流程
3. 查看生成的数据文件
"""

import asyncio
import json
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import argparse
import sys
import re
import glob

# === 内置配置类（原config.py内容）===

class Config:
    """配置类"""

    # Dune API 配置
    DUNE_API_KEY = 'aoIRZzGSZaHhJy8gTkHDeDpDYSMUQcDs'  # 请替换为您的实际API密钥
    DUNE_QUERY_ID = 4974281  # 请替换为您的实际查询ID
    DUNE_REQUEST_TIMEOUT = 300  # API请求超时时间（秒）

    # 文件路径配置
    DATA_DIR = "./data"  # 数据目录
    OUTPUT_DIR = "./output"  # 输出目录

    # 默认文件名
    DEFAULT_FILENAMES = {
        'dune_raw': 'dune_query_result_raw',
        'dune_enhanced': 'dune_query_result_enhanced',
        'telegram_input': 'telegram_messages.json',
        'telegram_output': 'telegram_data_processed',
        'merged_output': 'merged_data_final',
        'cpw_output': 'data_with_cpw'
    }

    # 时间差计算配置
    TIME_DIFF_CONFIG = {
        'required_columns': ['ath_time', 'atl_time', 'added_time'],
        'output_columns': ['ath_time_diff_seconds', 'atl_time_diff_seconds'],
        'time_formats': [
            "%Y-%m-%d %H:%M:%S.%f %Z",  # 2025-05-05 06:04:20.000 UTC
            "%Y-%m-%d %H:%M:%S.%f",     # 2025-05-05 06:04:20.000
            "%Y-%m-%d %H:%M:%S %Z",     # 2025-05-05 06:04:20 UTC
            "%Y-%m-%d %H:%M:%S",        # 2025-05-05 06:04:20
            "%Y-%m-%d %H:%M:%S+00:00",  # 2025-05-05 14:43:54+00:00
            "%Y-%m-%d %H:%M:%S%z",      # 2025-05-05 14:43:54+0000
            "%Y-%m-%d %H:%M %Z",        # 2025-05-05 14:43 UTC
            "%Y-%m-%d %H:%M",           # 2025-05-05 14:43
            "%Y-%m-%d"                  # 2025-05-05
        ]
    }

    # 表格合并配置
    MERGE_CONFIG = {
        'dune_match_column': 'token_address',  # Dune表格中的匹配列
        'telegram_match_column': '代币地址',  # Telegram表格中的匹配列
        'auto_detect_columns': True,  # 自动检测匹配列
        'preserve_formatting': True,  # 保持格式
        'handle_duplicates': 'first',  # 处理重复值的方式
        'auto_merge_enabled': True,  # 启用自动合并
        'merge_output_prefix': 'merged_data_final'  # 合并输出文件前缀
    }

    # Telegram配置
    TELEGRAM_CONFIG = {
        'api_id': 24702520,
        'api_hash': '7b1f880deb5998f36a079cfdbd097534',
        'session_name': 'telegram_cpw_finder_session',
        'channel_ids': [
            -1001914959004,
            -1002050218130
        ],
        'cpw_threshold': 278,
        'result_limit': 7,
        'search_delay': 1.6  # 搜索间隔（秒）
    }

    # 数据处理配置
    DATA_PROCESSING_CONFIG = {
        'encoding': 'utf-8-sig',  # 文件编码
        'decimal_places': 1,  # 小数位数
        'handle_missing_values': True,  # 处理缺失值
        'validate_data': True,  # 数据验证
        'create_backup': True  # 创建备份
    }

    # 代理配置
    PROXY_CONFIG = {
        'proxy_url': 'http://127.0.0.1:7890',  # 代理地址
        'enabled': True,  # 代理开关：True=使用代理，False=直接连接
        'timeout': 30  # 代理连接超时时间（秒）
    }

    # 日志配置
    LOGGING_CONFIG = {
        'level': 'INFO',
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'file_enabled': True,
        'console_enabled': True
    }

    @classmethod
    def ensure_directories(cls):
        """确保必要的目录存在"""
        directories = [cls.DATA_DIR, cls.OUTPUT_DIR]
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"创建目录: {directory}")

    @classmethod
    def get_timestamped_filename(cls, base_name, extension='csv'):
        """
        获取带时间戳的文件名

        Args:
            base_name (str): 基础文件名
            extension (str): 文件扩展名

        Returns:
            str: 带时间戳的完整文件名
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{base_name}_{timestamp}.{extension}"

    @classmethod
    def get_file_path(cls, filename, directory=None):
        """
        获取完整文件路径

        Args:
            filename (str): 文件名
            directory (str): 目录，默认为OUTPUT_DIR

        Returns:
            str: 完整文件路径
        """
        if directory is None:
            directory = cls.OUTPUT_DIR

        cls.ensure_directories()
        return os.path.join(directory, filename)

    @classmethod
    def get_dune_output_path(cls, enhanced=True):
        """
        获取Dune输出文件路径

        Args:
            enhanced (bool): 是否为增强版

        Returns:
            dict: 包含CSV和Excel路径的字典
        """
        base_name = cls.DEFAULT_FILENAMES['dune_enhanced' if enhanced else 'dune_raw']

        return {
            'csv': cls.get_file_path(cls.get_timestamped_filename(base_name, 'csv')),
            'excel': cls.get_file_path(cls.get_timestamped_filename(base_name, 'xlsx'))
        }

    @classmethod
    def get_telegram_paths(cls):
        """
        获取Telegram相关文件路径

        Returns:
            dict: 包含输入和输出路径的字典
        """
        return {
            'input': cls.get_file_path(cls.DEFAULT_FILENAMES['telegram_input'], cls.DATA_DIR),
            'output_csv': cls.get_file_path(cls.get_timestamped_filename(cls.DEFAULT_FILENAMES['telegram_output'], 'csv')),
            'output_excel': cls.get_file_path(cls.get_timestamped_filename(cls.DEFAULT_FILENAMES['telegram_output'], 'xlsx'))
        }

    @classmethod
    def get_merge_paths(cls):
        """
        获取合并文件路径

        Returns:
            dict: 包含合并输出路径的字典
        """
        return {
            'output_csv': cls.get_file_path(cls.get_timestamped_filename(cls.DEFAULT_FILENAMES['merged_output'], 'csv')),
            'output_excel': cls.get_file_path(cls.get_timestamped_filename(cls.DEFAULT_FILENAMES['merged_output'], 'xlsx'))
        }

    @classmethod
    def print_config_summary(cls):
        """打印配置摘要"""
        print("📋 当前配置摘要:")
        print("=" * 50)
        print(f"Dune查询ID: {cls.DUNE_QUERY_ID}")
        print(f"数据目录: {cls.DATA_DIR}")
        print(f"输出目录: {cls.OUTPUT_DIR}")
        print(f"Telegram频道数: {len(cls.TELEGRAM_CONFIG['channel_ids'])}")
        print(f"CPW阈值: {cls.TELEGRAM_CONFIG['cpw_threshold']}")
        print(f"文件编码: {cls.DATA_PROCESSING_CONFIG['encoding']}")
        print("=" * 50)

# 创建全局配置实例
config = Config()

# 确保目录存在
config.ensure_directories()

# 尝试导入Dune客户端
try:
    from dune_client.types import QueryParameter
    from dune_client.client import DuneClient
    from dune_client.query import QueryBase
    DUNE_AVAILABLE = True
except ImportError:
    print("警告：未安装dune_client库，请运行: pip install dune_client")
    DUNE_AVAILABLE = False

# 尝试导入Telegram客户端
try:
    from telethon import TelegramClient
    TELEGRAM_AVAILABLE = True
except ImportError:
    print("警告：未安装telethon库，请运行: pip install telethon")
    TELEGRAM_AVAILABLE = False

# === Telegram消息解析功能（来自telegram_to_excel.py）===

def extract_text_from_json(text_array):
    """从JSON文本数组中提取完整文本"""
    full_text = ""
    for item in text_array:
        if isinstance(item, str):
            full_text += item
        elif isinstance(item, dict) and "text" in item:
            full_text += item["text"]
    return full_text

def process_field_value(field_name, value, special_rules):
    """根据特殊规则处理字段值"""
    if not value:
        return None

    # 处理值为null的情况
    if value.lower() == "null" or value.strip() == "":
        return None

    # 移除可能的表情符号
    value = re.sub(r'[\U00010000-\U0010ffff\u2600-\u26FF\u2700-\u27BF]', '', value).strip()

    # 特殊处理网站和Telegram字段
    if field_name in ["网站", "Telegram"]:
        if value.lower() == "null" or value.strip() == "":
            return None
        # 检查是否包含有效的URL或文本内容
        if len(value.strip()) < 3:  # 太短的内容可能是错误提取的
            return None

    if field_name in special_rules:
        rule = special_rules[field_name]
        if rule == "only_number":
            # 只提取数字部分，移除逗号
            # 先移除逗号，再提取数字
            clean_value = value.replace(',', '')
            match = re.search(r'(\d+(?:\.\d+)?)', clean_value)
            return match.group(1) if match else value
        elif rule == "before_percent":
            # 提取百分号前的数字，移除逗号
            clean_value = value.replace(',', '')
            match = re.search(r'(\d+(?:\.\d+)?)\s*%', clean_value)
            return match.group(1) if match else value
        elif rule == "with_sign_before_percent":
            # 提取百分号前带加减号的数字
            match = re.search(r'([+-]\d+(?:\.\d+)?)\s*%', value)
            return match.group(1) if match else value
        elif rule == "full_text":
            # 保留完整文本
            return value
        elif rule == "remove_backticks":
            # 移除反引号
            return value.strip('`')
        elif rule == "description_check":
            # 描述字段特殊处理
            # 如果值为空、null或者只有空白字符，返回"0"
            if not value or value.lower() == "null" or value.strip() == "":
                return "0"
            # 如果值的长度大于等于7个字符，返回"1"
            elif len(value.strip()) >= 7:
                return "1"
            # 其他情况（值存在但长度小于7个字符），返回"0"
            else:
                return "0"
        elif rule == "before_slash_after_bracket":
            # 提取(45/45)中的45
            # 尝试多种模式匹配
            patterns = [
                r'\((\d+)/\d+\)',  # 匹配(45/45)
                r'\((\d+)\/\d+\)',  # 匹配(45/45)，使用转义的斜杠
                r'(\d+)/\d+',  # 匹配45/45（没有括号）
                r'(\d+)\/\d+'  # 匹配45/45（没有括号），使用转义的斜杠
            ]

            for pattern in patterns:
                match = re.search(pattern, value)
                if match:
                    return match.group(1)

            # 如果所有模式都不匹配，返回原始值
            return value
        elif rule == "before_slash":
            # 提取2/8中的2
            match = re.search(r'(\d+)\/\d+', value)
            return match.group(1) if match else value
        elif rule == "minutes_only":
            # 提取"239分钟. 2025-04-27 20:56:19"中的"239"
            match = re.search(r'(\d+)分钟', value)
            return match.group(1) if match else value
        elif rule == "buy_sell_first":
            # 从"买/卖:1/1次"中提取第一个数字 (1)，移除逗号
            clean_value = value.replace(',', '')
            match = re.search(r'(\d+)/\d+次', clean_value)
            return match.group(1) if match else value
        elif rule == "buy_sell_second":
            # 从"买/卖:1/1次"中提取第二个数字 (1)，移除逗号
            clean_value = value.replace(',', '')
            match = re.search(r'\d+/(\d+)次', clean_value)
            return match.group(1) if match else value
        elif rule == "dev_ratio_first":
            # 从"占比%:3.1/3.1"中提取斜杠前的数字 (3.1)，移除逗号
            clean_value = value.replace(',', '')
            match = re.search(r'(\d+(?:\.\d+)?)/\d+(?:\.\d+)?', clean_value)
            return match.group(1) if match else value
        elif rule == "wallet_count_with_percent":
            # 从"🐀老鼠钱包数量: 8, token占比: 44%"中提取数字 (8)，移除逗号
            clean_value = value.replace(',', '')
            match = re.search(r'(\d+),?\s*token占比', clean_value)
            return match.group(1) if match else value
        elif rule == "wallet_percent_only":
            # 从"🐀老鼠钱包数量: 8, token占比: 44%"中提取百分号前的数字 (44)，移除逗号
            clean_value = value.replace(',', '')
            match = re.search(r'token占比:\s*(\d+)%', clean_value)
            return match.group(1) if match else value
        elif rule == "datetime_to_minute":
            # 从"2025-05-20 05:32:23"中提取到分钟 ("2025-05-20 05:32")
            match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}):\d{2}', value)
            return match.group(1) if match else value
        elif rule == "tweet_count_only":
            # 从"发布推文2篇"中提取数字 (2)，移除逗号
            clean_value = value.replace(',', '')
            match = re.search(r'发布推文(\d+)篇', clean_value)
            return match.group(1) if match else value
        elif rule == "average_view_only":
            # 从"账号平均每条 浏览180"中提取数字 (180)，移除逗号
            clean_value = value.replace(',', '')
            match = re.search(r'浏览(\d+)', clean_value)
            return match.group(1) if match else value

    return value

def parse_telegram_message(text_array):
    """解析电报消息的JSON格式，提取字段和值"""
    # 提取所有字段
    extracted_data = {}

    # 检查text_array的结构，如果是纯字符串数组，则合并为单个字符串处理
    if text_array and all(isinstance(item, str) for item in text_array):
        # 纯字符串数组，合并为单个字符串
        full_text = "".join(text_array)
        # 将合并后的字符串重新放入数组，以便后续处理逻辑可以正常工作
        text_array = [full_text]

    # 构建完整文本，用于后续查找
    full_text = ""
    for item in text_array:
        if isinstance(item, str):
            full_text += item
        elif isinstance(item, dict) and "text" in item:
            full_text += item["text"]

    # 由于实际数据中text_array可能是字符串数组，我们主要使用full_text进行解析
    # 但保留原有的数组遍历逻辑以防某些数据格式不同

    # 由于数据是纯字符串，直接从full_text中提取所有字段
    # 先处理特殊的多值字段（老鼠仓）
    mouse_cage_values = []
    mouse_cage_pattern = r'🐀[^:]*?老鼠仓[^:]*?:\s*([^\n]+)'
    mouse_cage_matches = re.finditer(mouse_cage_pattern, full_text)
    for match in mouse_cage_matches:
        value_part = match.group(1).strip()
        # 处理值为null的情况
        if value_part.lower() == "null" or value_part.lower() == "null🐀":
            value_part = "null"
        else:
            # 提取数字部分
            number_match = re.search(r'(\d+)', value_part)
            if number_match:
                value_part = number_match.group(1)
            # 移除可能的表情符号
            value_part = re.sub(r'[\U00010000-\U0010ffff\u2600-\u26FF\u2700-\u27BF]', '', value_part).strip()
        mouse_cage_values.append(value_part)

    # 处理Pro Traders字段的特殊情况
    pro_traders_value = None
    pro_traders_patterns = [
        r'👨‍💼[^:]*?Pro\s*Traders[^:]*?:\s*([^\n]+)',
        r'Pro\s*Traders[^:]*?:\s*([^\n]+)',
        r'👨‍💼[^:]*?:\s*(\d+)'
    ]
    for pattern in pro_traders_patterns:
        pro_match = re.search(pattern, full_text)
        if pro_match:
            pro_traders_value = pro_match.group(1).strip()
            break

    # 定义需要提取的字段和特殊处理规则
    fields_to_extract = {
        "代币地址": "remove_backticks",
        "名称": "full_text",
        "符号": "full_text",
        "描述": "description_check",
        "创建者": "remove_backticks",
        "创建时间": "minutes_only",  # 只提取分钟前的数值
        "山丘之王时间戳": "minutes_only",  # 只提取分钟前的数值
        "进程1阶段": "only_number",  # 确保这个字段被包含
        "回复数": "only_number",
        "值定曲线": "full_text",
        "历史最高市值": "only_number",
        "Top 10 Holders": "before_percent",
        "Dev Holders": "before_percent",  # 确保这个字段被包含
        "Snipers Holders": "before_percent",
        "Insiders": "before_percent",
        "Holders": "only_number",
        "Pro Traders": "only_number",
        "Bundlers": "before_percent",
        "Buys": "full_text",
        "Sells": "full_text",
        "网站": "full_text",
        "Twitter": "full_text",
        "Telegram": "full_text",
        "Insider Holding": "before_percent",
        "Dev Holding": "before_percent",
        "Bot Users": "only_number",
        "DexPaid支付时间": "minutes_only",  # 只提取分钟前的数值
        "聪明钱": "only_number",
        "KOL/VC": "only_number",
        "开发者": "only_number",  # 确保这个字段被包含
        "开发者(DEV)": "only_number",  # 新增字段
        "鲸鱼": "only_number",
        "巨鲸": "only_number",  # 新增字段
        "新钱包": "only_number",
        "狙击者": "only_number",
        "持币大户": "only_number",
        "老鼠仓": "only_number",
        "持有者数量": "only_number",
        "老鼠仓百分比": "before_percent",
        "跑路百分比": "before_percent",
        "跑路比例": "before_slash_after_bracket",
        "Top 1 持有比例": "before_percent",
        "Top50状态": "before_slash",  # 新增字段，只取/前面的数字
        "24H换手率": "before_percent",  # 新增字段
        "钓鱼地址": "full_text",  # 新增字段
        "阴谋集团": "full_text",  # 新增字段
        "1m涨跌幅": "with_sign_before_percent",  # 新增字段，提取百分号前带加减号的数字
        "检测时间": "full_text",
        # 猎人频道信息新增字段
        "买次数": "buy_sell_first",  # 从"买/卖:1/1次"中提取第一个数字
        "卖次数": "buy_sell_second",  # 从"买/卖:1/1次"中提取第二个数字
        "DEV占比": "dev_ratio_first",  # 从"占比%:3.1/3.1"中提取斜杠前的数字
        "老鼠钱包数量": "wallet_count_with_percent",  # 从"🐀老鼠钱包数量: 8, token占比: 44%"中提取数字
        "老鼠钱包token占比": "wallet_percent_only",  # 从"🐀老鼠钱包数量: 8, token占比: 44%"中提取百分号前的数字
        "新钱包数量": "wallet_count_with_percent",  # 从"🟢新钱包数量: 11, token占比: 49%"中提取数字
        "新钱包token占比": "wallet_percent_only",  # 从"🟢新钱包数量: 11, token占比: 49%"中提取百分号前的数字
        "总计聪明钱": "only_number",  # 从"总计聪明钱： 0"中提取数字
        "标签": "full_text",  # 从"标签："后提取文本内容
        "TGCall数量": "only_number",  # 从"✈️TGCall：4"中提取数字
        "TGCaller名称": "full_text",  # TGCall行下一行的完整文本内容
        "粉丝数": "only_number",  # 从"粉丝数: 3"中提取数字
        "账号创建时间": "datetime_to_minute",  # 从"账号创建时间: 2025-05-20 05:32:23"中提取到分钟
        "发布推文数": "tweet_count_only",  # 从"发布推文2篇"中提取数字
        "平均浏览量": "average_view_only",  # 从"账号平均每条 浏览180"中提取数字
        "平均评论数": "only_number",  # 从"评论0"中提取数字
        "平均转推数": "only_number",  # 从"转推0"中提取数字
        "平均点赞数": "only_number",  # 从"点赞7"中提取数字
        # 新增推特相关字段
        "CA关联推文": "only_number",  # 从"🐦CA关联推文：3"中提取数字
        "推特喊单用户数": "only_number",  # 从"🐦推特喊单用户数: 3"中提取数字
        "推特caller名称": "full_text",  # 从推特喊单用户数后提取用户名列表
        "推特订阅浏览量": "only_number",  # 从"🔹推特订阅浏览量：0"中提取数字
        "粉丝覆盖量": "only_number"  # 从"⚡粉丝覆盖量：3420"中提取数字
    }

    # 忽略的字段（不需要提取的字段）
    ignore_fields = [
        "虚拟代币储备",
        "DexPaid状态",
        "跑路概率",
        "新代币检测",
        "Axiom指标",
        "BullX指标",
        "GMGN指标",
        "相关消息数量",
        "Telegram频道信息",
        "猎人频道信息"
    ]

    # 使用正则表达式从完整文本中提取字段和值
    # 修改正则表达式，更精确地匹配字段和值，避免空值时匹配到下一行
    pattern = r'([^\n:]+):\s*([^\n]*?)(?=\n|$)'
    matches = re.finditer(pattern, full_text)

    # 特别关注的字段 - 包含所有需要提取的字段
    special_fields = [
        "代币地址", "名称", "符号", "描述", "创建者", "创建时间", "山丘之王时间戳",
        "进程1阶段", "回复数", "值定曲线", "历史最高市值", "Top 10 Holders",
        "Dev Holders", "Snipers Holders", "Insiders", "Holders", "Pro Traders",
        "Bundlers", "Buys", "Sells", "网站", "Twitter", "Telegram",
        "Insider Holding", "Dev Holding", "Bot Users", "DexPaid支付时间",
        "聪明钱", "KOL/VC", "开发者", "开发者(DEV)", "鲸鱼", "巨鲸", "新钱包",
        "狙击者", "持币大户", "老鼠仓", "老鼠仓2", "持有者数量", "老鼠仓百分比",
        "跑路百分比", "跑路比例", "Top 1 持有比例", "Top50状态", "24H换手率",
        "钓鱼地址", "阴谋集团", "1m涨跌幅", "检测时间",
        # 猎人频道信息字段
        "买次数", "卖次数", "DEV占比", "老鼠钱包数量", "老鼠钱包token占比",
        "新钱包数量", "新钱包token占比", "总计聪明钱", "标签", "TGCall数量",
        "TGCaller名称", "粉丝数", "账号创建时间", "发布推文数", "平均浏览量",
        "平均评论数", "平均转推数", "平均点赞数",
        # 新增推特相关字段
        "CA关联推文", "推特喊单用户数", "推特caller名称", "推特订阅浏览量", "粉丝覆盖量"
    ]

    for match in matches:
        field = match.group(1).strip()
        # 移除字段前的表情符号
        field = re.sub(r'^[\U00010000-\U0010ffff\u2600-\u26FF\u2700-\u27BF]+\s*', '', field)

        # 检查是否是需要提取的字段
        if field in fields_to_extract and field not in ignore_fields and field not in extracted_data:
            value = match.group(2).strip()

            # 特殊处理网站和Telegram字段
            if field in ["网站", "Telegram"]:
                # 如果值为null或空，设置为None
                if value.lower() == "null" or value.strip() == "":
                    extracted_data[field] = None
                # 检查值是否包含有效的URL
                elif "http" in value or "www" in value:
                    processed_value = process_field_value(field, value, fields_to_extract)
                    extracted_data[field] = processed_value
                # 如果值看起来不像URL，可能是错误提取的内容
                else:
                    extracted_data[field] = None
            else:
                # 检查值是否为空或只包含空白字符
                if not value or value.strip() == "":
                    extracted_data[field] = None
                else:
                    # 特殊处理持有者数量字段，避免提取到其他内容
                    if field == "持有者数量":
                        # 检查值是否是纯数字，如果不是则认为是错误提取
                        if value.strip().replace(',', '').isdigit():
                            processed_value = process_field_value(field, value, fields_to_extract)
                            extracted_data[field] = processed_value
                        else:
                            extracted_data[field] = None
                    else:
                        processed_value = process_field_value(field, value, fields_to_extract)
                        extracted_data[field] = processed_value

    # 如果之前找到了Pro Traders字段，直接添加到提取的数据中
    if pro_traders_value is not None:
        processed_value = process_field_value("Pro Traders", pro_traders_value, fields_to_extract)
        extracted_data["Pro Traders"] = processed_value

    # 处理找到的老鼠仓字段
    if len(mouse_cage_values) >= 1:
        # 第一个老鼠仓值
        value = mouse_cage_values[0]
        if value.lower() == "null":
            extracted_data["老鼠仓"] = None
        else:
            processed_value = process_field_value("老鼠仓", value, fields_to_extract)
            extracted_data["老鼠仓"] = processed_value

        # 如果有第二个老鼠仓值，添加为老鼠仓2
        if len(mouse_cage_values) >= 2:
            value = mouse_cage_values[1]
            if value.lower() == "null":
                extracted_data["老鼠仓2"] = None
            else:
                processed_value = process_field_value("老鼠仓", value, fields_to_extract)
                extracted_data["老鼠仓2"] = processed_value

    # 特殊处理猎人频道信息字段 - 直接在完整文本中搜索
    # 处理买/卖次数
    buy_sell_pattern = r'买/卖:(\d+)/(\d+)次'
    buy_sell_match = re.search(buy_sell_pattern, full_text)
    if buy_sell_match:
        extracted_data["买次数"] = buy_sell_match.group(1)
        extracted_data["卖次数"] = buy_sell_match.group(2)

    # 处理DEV占比
    dev_ratio_pattern = r'占比%:(\d+(?:\.\d+)?)/\d+(?:\.\d+)?'
    dev_ratio_match = re.search(dev_ratio_pattern, full_text)
    if dev_ratio_match:
        extracted_data["DEV占比"] = dev_ratio_match.group(1)

    # 处理老鼠钱包信息
    mouse_wallet_pattern = r'🐀老鼠钱包数量:\s*(\d+),\s*token占比:\s*(\d+)%'
    mouse_wallet_match = re.search(mouse_wallet_pattern, full_text)
    if mouse_wallet_match:
        extracted_data["老鼠钱包数量"] = mouse_wallet_match.group(1)
        extracted_data["老鼠钱包token占比"] = mouse_wallet_match.group(2)

    # 处理新钱包信息
    new_wallet_pattern = r'🟢新钱包数量:\s*(\d+),\s*token占比:\s*(\d+)%'
    new_wallet_match = re.search(new_wallet_pattern, full_text)
    if new_wallet_match:
        extracted_data["新钱包数量"] = new_wallet_match.group(1)
        extracted_data["新钱包token占比"] = new_wallet_match.group(2)

    # 处理总计聪明钱
    smart_money_pattern = r'总计聪明钱：\s*(\d+)'
    smart_money_match = re.search(smart_money_pattern, full_text)
    if smart_money_match:
        extracted_data["总计聪明钱"] = smart_money_match.group(1)

    # 处理标签 - 更精确的匹配，只匹配同一行内容
    tag_pattern = r'标签：\s*([^\n]*?)(?=\n|$)'
    tag_match = re.search(tag_pattern, full_text)
    if tag_match:
        tag_value = tag_match.group(1).strip()
        # 如果标签值为空或包含其他内容（如总计、🟩等），则认为是空标签
        if tag_value and not any(keyword in tag_value for keyword in ['总计', '🟩', '🟥', 'TGCall', '✈️', 'THE DEGEN', 'SOL', '💙', '🎲']):
            extracted_data["标签"] = tag_value
        else:
            extracted_data["标签"] = None
    else:
        extracted_data["标签"] = None

    # 处理TGCall数量 - 使用更宽松的模式
    tgcall_pattern = r'TGCall：(\d+)'
    tgcall_match = re.search(tgcall_pattern, full_text)
    if tgcall_match:
        extracted_data["TGCall数量"] = tgcall_match.group(1)

    # 处理TGCaller名称（TGCall行的下一行）- 只有当TGCall数量大于0时才有内容
    tgcaller_pattern = r'TGCall：(\d+)\n([^\n]+)'
    tgcaller_match = re.search(tgcaller_pattern, full_text)
    if tgcaller_match:
        tgcall_count = int(tgcaller_match.group(1))
        if tgcall_count > 0:
            caller_name = tgcaller_match.group(2).strip()
            # 确保不是空行或其他无关内容
            if caller_name and not caller_name.startswith('name:') and not caller_name.startswith('USER:'):
                extracted_data["TGCaller名称"] = caller_name
            else:
                extracted_data["TGCaller名称"] = None
        else:
            extracted_data["TGCaller名称"] = None
    else:
        extracted_data["TGCaller名称"] = None

    # 处理粉丝数
    fans_pattern = r'粉丝数:\s*(\d+)'
    fans_match = re.search(fans_pattern, full_text)
    if fans_match:
        extracted_data["粉丝数"] = fans_match.group(1)

    # 处理账号创建时间
    create_time_pattern = r'账号创建时间:\s*(\d{4}-\d{2}-\d{2} \d{2}:\d{2}):\d{2}'
    create_time_match = re.search(create_time_pattern, full_text)
    if create_time_match:
        extracted_data["账号创建时间"] = create_time_match.group(1)

    # 处理发布推文数
    tweet_count_pattern = r'发布推文(\d+)篇'
    tweet_count_match = re.search(tweet_count_pattern, full_text)
    if tweet_count_match:
        extracted_data["发布推文数"] = tweet_count_match.group(1)

    # 处理平均浏览量
    view_pattern = r'浏览(\d+)'
    view_match = re.search(view_pattern, full_text)
    if view_match:
        extracted_data["平均浏览量"] = view_match.group(1)

    # 处理平均评论数
    comment_pattern = r'评论(\d+)'
    comment_match = re.search(comment_pattern, full_text)
    if comment_match:
        extracted_data["平均评论数"] = comment_match.group(1)

    # 处理平均转推数
    retweet_pattern = r'转推(\d+)'
    retweet_match = re.search(retweet_pattern, full_text)
    if retweet_match:
        extracted_data["平均转推数"] = retweet_match.group(1)

    # 处理平均点赞数
    like_pattern = r'点赞(\d+)'
    like_match = re.search(like_pattern, full_text)
    if like_match:
        extracted_data["平均点赞数"] = like_match.group(1)

    # 特殊处理：直接从文本中提取包含逗号的数字字段
    # 处理平均浏览量（包含逗号的情况）
    view_pattern_with_comma = r'浏览(\d+(?:,\d+)*)'
    view_match_comma = re.search(view_pattern_with_comma, full_text)
    if view_match_comma:
        view_value = view_match_comma.group(1).replace(',', '')
        extracted_data["平均浏览量"] = view_value

    # 处理平均评论数（包含逗号的情况）
    comment_pattern_with_comma = r'评论(\d+(?:,\d+)*)'
    comment_match_comma = re.search(comment_pattern_with_comma, full_text)
    if comment_match_comma:
        comment_value = comment_match_comma.group(1).replace(',', '')
        extracted_data["平均评论数"] = comment_value

    # 处理平均转推数（包含逗号的情况）
    retweet_pattern_with_comma = r'转推(\d+(?:,\d+)*)'
    retweet_match_comma = re.search(retweet_pattern_with_comma, full_text)
    if retweet_match_comma:
        retweet_value = retweet_match_comma.group(1).replace(',', '')
        extracted_data["平均转推数"] = retweet_value

    # 处理平均点赞数（包含逗号的情况）
    like_pattern_with_comma = r'点赞(\d+(?:,\d+)*)'
    like_match_comma = re.search(like_pattern_with_comma, full_text)
    if like_match_comma:
        like_value = like_match_comma.group(1).replace(',', '')
        extracted_data["平均点赞数"] = like_value

    # 处理新增的推特相关字段
    # 处理CA关联推文
    ca_tweet_pattern = r'🐦CA关联推文：(\d+(?:,\d+)*)'
    ca_tweet_match = re.search(ca_tweet_pattern, full_text)
    if ca_tweet_match:
        ca_tweet_value = ca_tweet_match.group(1).replace(',', '')
        extracted_data["CA关联推文"] = ca_tweet_value

    # 处理推特喊单用户数和推特caller名称
    twitter_caller_pattern = r'🐦推特喊单用户数:\s*(\d+(?:,\d+)*)\s*([^\n]*?)(?=\n|$)'
    twitter_caller_match = re.search(twitter_caller_pattern, full_text)
    if twitter_caller_match:
        # 提取推特喊单用户数
        caller_count = twitter_caller_match.group(1).replace(',', '')
        extracted_data["推特喊单用户数"] = caller_count

        # 提取推特caller名称（只有当推特喊单用户数不为空且不为0时才提取）
        caller_names = twitter_caller_match.group(2).strip()
        if caller_count and caller_count != "0" and caller_names:
            extracted_data["推特caller名称"] = caller_names
        else:
            extracted_data["推特caller名称"] = None
    else:
        extracted_data["推特喊单用户数"] = None
        extracted_data["推特caller名称"] = None

    # 处理推特订阅浏览量
    twitter_view_pattern = r'🔹推特订阅浏览量：(\d+(?:,\d+)*)'
    twitter_view_match = re.search(twitter_view_pattern, full_text)
    if twitter_view_match:
        twitter_view_value = twitter_view_match.group(1).replace(',', '')
        extracted_data["推特订阅浏览量"] = twitter_view_value

    # 处理粉丝覆盖量
    fan_coverage_pattern = r'⚡粉丝覆盖量：(\d+(?:,\d+)*)'
    fan_coverage_match = re.search(fan_coverage_pattern, full_text)
    if fan_coverage_match:
        fan_coverage_value = fan_coverage_match.group(1).replace(',', '')
        extracted_data["粉丝覆盖量"] = fan_coverage_value

    # 处理其他可能包含逗号的数字字段
    comma_number_fields = [
        ("粉丝数", r'粉丝数:\s*(\d+(?:,\d+)*)'),
        ("老鼠钱包数量", r'老鼠钱包数量:\s*(\d+(?:,\d+)*)'),
        ("新钱包数量", r'新钱包数量:\s*(\d+(?:,\d+)*)'),
        ("总计聪明钱", r'总计聪明钱：\s*(\d+(?:,\d+)*)'),
        ("TGCall数量", r'TGCall：(\d+(?:,\d+)*)'),
        ("发布推文数", r'发布推文(\d+(?:,\d+)*)篇'),
        ("持有者数量", r'持有者数量[^\n]*?:\s*(\d+(?:,\d+)*)\s*(?=\n|$)')
    ]

    for field_name, pattern in comma_number_fields:
        # 特殊处理持有者数量字段
        if field_name == "持有者数量":
            # 更精确的匹配：确保数字紧跟在冒号后面，在同一行
            holder_pattern = r'持有者数量[^\n]*?:\s*(\d+(?:,\d+)*)\s*(?=\n|$)'
            holder_match = re.search(holder_pattern, full_text)
            if holder_match:
                clean_value = holder_match.group(1).replace(',', '')
                extracted_data[field_name] = clean_value
            # 如果没有匹配到，检查是否是空值情况
            else:
                empty_holder_pattern = r'持有者数量[^\n]*?:\s*$'
                empty_match = re.search(empty_holder_pattern, full_text, re.MULTILINE)
                if empty_match:
                    extracted_data[field_name] = None
        else:
            match = re.search(pattern, full_text)
            if match:
                clean_value = match.group(1).replace(',', '')
                extracted_data[field_name] = clean_value

    # 检查特别关注的字段是否已提取
    for field in special_fields:
        if field not in extracted_data:
            # 尝试使用更宽松的模式再次查找，但避免匹配到下一行内容
            loose_pattern = rf'{field}[^\n]*?:\s*([^\n]*?)(?=\n|$)'
            loose_match = re.search(loose_pattern, full_text, re.IGNORECASE)
            if loose_match:
                value = loose_match.group(1).strip()

                # 特殊处理网站和Telegram字段
                if field in ["网站", "Telegram"]:
                    # 如果值为null或空，设置为None
                    if value.lower() == "null" or value.strip() == "":
                        extracted_data[field] = None
                    # 检查值是否包含有效的URL
                    elif "http" in value or "www" in value:
                        processed_value = process_field_value(field, value, fields_to_extract)
                        extracted_data[field] = processed_value
                    # 如果值看起来不像URL，可能是错误提取的内容
                    else:
                        extracted_data[field] = None
                else:
                    # 检查值是否为空或只包含空白字符
                    if not value or value.strip() == "":
                        extracted_data[field] = None
                    else:
                        processed_value = process_field_value(field, value, fields_to_extract)
                        extracted_data[field] = processed_value

    return extracted_data

# === 字段编码功能（来自excel_processor_optimized.py）===

def encode_website_column(value):
    """编码网站列的值
    0=空值，1=包含app，2=包含pump.fun/bit.ly/discord/t.co/t.me，
    3=包含truthsocial/twitter/tiktok/instagram/reddit/x.com/youtu，4=其他
    """
    if pd.isna(value) or value == '' or str(value).strip() == '':
        return 0

    value_str = str(value).lower().strip()

    # 检查是否包含app
    if 'app' in value_str:
        return 1

    # 检查第2类关键词
    type2_keywords = ['https://pump.fun', 'https://bit.ly', 'discord', 'https://t.co', 'https://t.me']
    for keyword in type2_keywords:
        if keyword.lower() in value_str:
            return 2

    # 检查第3类关键词
    type3_keywords = ['https://truthsocial', 'https://twitter', 'tiktok', 'instagram', 'reddit', 'https://x.com', 'youtu']
    for keyword in type3_keywords:
        if keyword.lower() in value_str:
            return 3

    # 其他所有内容
    return 4

def encode_twitter_column(value):
    """编码Twitter列的值
    0=空值，1=twitter.com/x.com+/status，2=twitter.com/x.com+i/communities，
    3=twitter.com/x.com但排除前两种，4=其他
    """
    if pd.isna(value) or value == '' or str(value).strip() == '':
        return 0

    value_str = str(value).lower().strip()

    # 检查是否包含twitter.com或x.com
    has_twitter = 'twitter.com' in value_str or 'x.com' in value_str

    if has_twitter:
        # 检查是否包含/status模式
        if '/status' in value_str:
            return 1
        # 检查是否包含i/communities模式
        elif 'i/communities' in value_str:
            return 2
        # 其他twitter.com/x.com内容
        else:
            return 3

    # 不包含twitter.com或x.com的其他内容
    return 4

def encode_telegram_column(value):
    """编码Telegram列的值
    0=空值，1=包含https://t.me，2=其他
    """
    if pd.isna(value) or value == '' or str(value).strip() == '':
        return 0

    value_str = str(value).lower().strip()

    # 检查是否包含https://t.me
    if 'https://t.me' in value_str:
        return 1

    # 其他所有内容
    return 2

def apply_field_encoding(df):
    """对DataFrame中的网站、Twitter、Telegram字段进行编码
    在原始列旁边新增对应的编码列
    """
    print("🔧 开始字段编码转换...")

    df_processed = df.copy()
    encoding_stats = {}

    try:
        # 处理网站列
        if '网站' in df_processed.columns:
            df_processed['网站_编码'] = df_processed['网站'].apply(encode_website_column)
            encoded_values = df_processed['网站_编码'].value_counts().sort_index()
            encoding_stats['网站'] = dict(encoded_values)
            print(f"   ✅ 网站: 编码转换完成，编码分布: {dict(encoded_values)}")
        else:
            print(f"   ℹ️ 网站: 列不存在，跳过处理")

        # 处理Twitter列
        if 'Twitter' in df_processed.columns:
            df_processed['Twitter_编码'] = df_processed['Twitter'].apply(encode_twitter_column)
            encoded_values = df_processed['Twitter_编码'].value_counts().sort_index()
            encoding_stats['Twitter'] = dict(encoded_values)
            print(f"   ✅ Twitter: 编码转换完成，编码分布: {dict(encoded_values)}")
        else:
            print(f"   ℹ️ Twitter: 列不存在，跳过处理")

        # 处理Telegram列
        if 'Telegram' in df_processed.columns:
            df_processed['Telegram_编码'] = df_processed['Telegram'].apply(encode_telegram_column)
            encoded_values = df_processed['Telegram_编码'].value_counts().sort_index()
            encoding_stats['Telegram'] = dict(encoded_values)
            print(f"   ✅ Telegram: 编码转换完成，编码分布: {dict(encoded_values)}")
        else:
            print(f"   ℹ️ Telegram: 列不存在，跳过处理")

        print("✅ 字段编码转换完成")
        return df_processed, encoding_stats

    except Exception as e:
        print(f"❌ 字段编码转换过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return df, {}

class IntegratedDataProcessor:
    """集成数据处理器类"""

    def __init__(self):
        """初始化集成数据处理器"""
        # Dune配置
        self.dune_api_key = config.DUNE_API_KEY
        self.dune_query_id = config.DUNE_QUERY_ID
        self.dune_client = None

        # Telegram配置
        self.telegram_api_id = config.TELEGRAM_CONFIG['api_id']
        self.telegram_api_hash = config.TELEGRAM_CONFIG['api_hash']
        self.telegram_session_name = config.TELEGRAM_CONFIG['session_name']
        self.target_channel_id = -1002327446504  # 指定的群组ID
        self.telegram_client = None

        # 代理配置
        self.proxy_enabled = config.PROXY_CONFIG['enabled']
        self.proxy_url = config.PROXY_CONFIG['proxy_url']
        self.proxy_timeout = config.PROXY_CONFIG['timeout']

        # 初始化客户端
        self._init_clients()

    def _init_clients(self):
        """初始化API客户端"""
        # 初始化Dune客户端
        if DUNE_AVAILABLE:
            # 设置代理（如果启用）
            if self.proxy_enabled:
                print(f"🌐 Dune客户端使用代理: {self.proxy_url}")
                # 设置环境变量方式的代理
                import os
                os.environ['HTTP_PROXY'] = self.proxy_url
                os.environ['HTTPS_PROXY'] = self.proxy_url
                os.environ['http_proxy'] = self.proxy_url
                os.environ['https_proxy'] = self.proxy_url
            else:
                # 清除代理环境变量
                import os
                for proxy_var in ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']:
                    if proxy_var in os.environ:
                        del os.environ[proxy_var]

            self.dune_client = DuneClient(
                api_key=self.dune_api_key,
                base_url="https://api.dune.com",
                request_timeout=config.DUNE_REQUEST_TIMEOUT
            )

        # 初始化Telegram客户端（代理设置将在连接时处理）
        if TELEGRAM_AVAILABLE:
            proxy_settings = None
            if self.proxy_enabled:
                # 解析代理URL
                import urllib.parse
                parsed = urllib.parse.urlparse(self.proxy_url)
                proxy_settings = {
                    'proxy_type': 'http',
                    'addr': parsed.hostname,
                    'port': parsed.port
                }
                print(f"🌐 Telegram客户端使用代理: {self.proxy_url}")

            self.telegram_client = TelegramClient(
                self.telegram_session_name,
                self.telegram_api_id,
                self.telegram_api_hash,
                proxy=proxy_settings
            )

    def print_proxy_status(self):
        """打印代理配置状态"""
        print("🌐 代理配置状态:")
        print("=" * 30)
        if self.proxy_enabled:
            print(f"✅ 代理已启用: {self.proxy_url}")
            print(f"⏱️ 超时时间: {self.proxy_timeout}秒")
            # 测试代理连接
            self.test_proxy_connection()
        else:
            print("❌ 代理已禁用，使用直接连接")
        print("=" * 30)

    def test_proxy_connection(self):
        """测试代理连接"""
        print("🔍 测试代理连接...")
        try:
            import requests
            import urllib.parse

            # 解析代理URL
            parsed = urllib.parse.urlparse(self.proxy_url)
            proxies = {
                'http': self.proxy_url,
                'https': self.proxy_url
            }

            # 测试连接到一个简单的网站
            response = requests.get(
                'http://httpbin.org/ip',
                proxies=proxies,
                timeout=10
            )

            if response.status_code == 200:
                print("✅ 代理连接测试成功")
                result = response.json()
                print(f"  - 当前IP: {result.get('origin', '未知')}")
            else:
                print(f"⚠️ 代理连接测试失败，状态码: {response.status_code}")

        except Exception as e:
            print(f"❌ 代理连接测试失败: {e}")
            print("💡 建议检查:")
            print("  1. 代理服务是否正在运行")
            print("  2. 代理地址和端口是否正确")
            print("  3. 防火墙设置是否阻止连接")

    # === Dune数据处理功能 ===

    def download_dune_data(self):
        """从Dune API下载数据"""
        if not DUNE_AVAILABLE:
            raise ImportError("Dune客户端不可用，请安装dune_client库")

        print(f"📊 正在从Dune API获取查询 {self.dune_query_id} 的数据...")

        try:
            query_result = self.dune_client.get_latest_result_dataframe(query=self.dune_query_id)
            print(f"✅ 成功获取数据，共 {len(query_result)} 行，{len(query_result.columns)} 列")
            return query_result
        except Exception as e:
            print(f"❌ 获取Dune数据时出错: {e}")
            raise

    def calculate_time_differences(self, df):
        """计算时间差值并添加新列"""
        print("⏱️ 开始计算时间差...")

        # 检查必需的列是否存在
        required_columns = config.TIME_DIFF_CONFIG['required_columns']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            print(f"⚠️ 警告：缺少以下列，将跳过时间差计算: {missing_columns}")
            return df

        # 转换时间列为datetime格式
        def parse_time_column(series, col_name):
            """解析时间列，支持多种格式"""
            try:
                return pd.to_datetime(series, errors='coerce')
            except Exception as e:
                print(f"转换{col_name}列时出错: {e}")
                return series

        # 转换时间列
        df['ath_time_parsed'] = parse_time_column(df['ath_time'], 'ath_time')
        df['atl_time_parsed'] = parse_time_column(df['atl_time'], 'atl_time')
        df['added_time_parsed'] = parse_time_column(df['added_time'], 'added_time')

        # 计算时间差（秒）
        def calculate_seconds_diff(time1, time2):
            """计算两个时间之间的秒数差值"""
            try:
                if pd.isna(time1) or pd.isna(time2):
                    return np.nan
                diff = (time1 - time2).total_seconds()
                return int(diff)  # 返回整数秒
            except Exception:
                return np.nan

        # 计算ath_time - added_time的时间差
        df['ath_time_diff_seconds'] = df.apply(
            lambda row: calculate_seconds_diff(row['ath_time_parsed'], row['added_time_parsed']),
            axis=1
        )

        # 计算atl_time - added_time的时间差
        df['atl_time_diff_seconds'] = df.apply(
            lambda row: calculate_seconds_diff(row['atl_time_parsed'], row['added_time_parsed']),
            axis=1
        )

        # 删除临时的解析列
        df = df.drop(['ath_time_parsed', 'atl_time_parsed', 'added_time_parsed'], axis=1)

        # 统计计算结果
        ath_valid = df['ath_time_diff_seconds'].notna().sum()
        atl_valid = df['atl_time_diff_seconds'].notna().sum()
        total_rows = len(df)

        print(f"✅ 时间差计算完成:")
        print(f"  - 总行数: {total_rows}")
        print(f"  - ATH时间差有效值: {ath_valid}")
        print(f"  - ATL时间差有效值: {atl_valid}")

        return df

    def save_dune_results(self, df):
        """保存Dune处理结果"""
        print("💾 正在保存Dune处理结果...")

        # 获取输出路径
        file_paths = config.get_dune_output_path(enhanced=True)

        # 保存CSV
        df.to_csv(file_paths['csv'], index=False, encoding=config.DATA_PROCESSING_CONFIG['encoding'])
        print(f"✅ CSV文件已保存: {file_paths['csv']}")

        # 保存Excel
        df.to_excel(file_paths['excel'], index=False)
        print(f"✅ Excel文件已保存: {file_paths['excel']}")

        return file_paths

    def show_dune_sample_data(self, df):
        """显示Dune数据示例"""
        print("\n📋 Dune数据示例:")
        print("=" * 50)

        # 显示基本信息
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")

        # 显示新增列的示例数据
        if 'ath_time_diff_seconds' in df.columns:
            print("\n⏱️ 新增时间差列示例 (前5行):")
            time_diff_cols = ['ath_time', 'atl_time', 'added_time', 'ath_time_diff_seconds', 'atl_time_diff_seconds']
            available_cols = [col for col in time_diff_cols if col in df.columns]
            print(df[available_cols].head())

        print("=" * 50)

    # === Telegram消息处理功能 ===

    async def connect_telegram(self):
        """连接到Telegram"""
        if not TELEGRAM_AVAILABLE:
            raise ImportError("Telegram客户端不可用，请安装telethon库")

        print("🔗 正在连接到Telegram...")
        await self.telegram_client.start()
        print("✅ 已连接到Telegram")

    async def disconnect_telegram(self):
        """断开Telegram连接"""
        if self.telegram_client:
            await self.telegram_client.disconnect()
            print("✅ 已断开Telegram连接")

    def analyze_dune_timerange(self, dune_df):
        """分析Dune数据的时间范围"""
        print("📅 分析Dune数据时间范围...")

        try:
            if 'added_time' not in dune_df.columns:
                raise ValueError("Dune数据中缺少 'added_time' 列")

            print(f"  - 总记录数: {len(dune_df)}")
            print(f"  - added_time列样本: {dune_df['added_time'].head(3).tolist()}")

            # 解析时间列
            dune_df['added_time_parsed'] = pd.to_datetime(dune_df['added_time'], errors='coerce')
            valid_times = dune_df['added_time_parsed'].dropna()

            if len(valid_times) == 0:
                raise ValueError("没有有效的时间数据")

            print(f"  - 有效时间记录: {len(valid_times)}")

            # 获取最早和最晚的日期（精确到日）
            min_datetime = valid_times.min()
            max_datetime = valid_times.max()

            # 转换为日期（去掉时间部分）
            original_start_date = min_datetime.date()
            original_end_date = max_datetime.date()

            # 为Telegram下载扩展时间范围（前后各加1天）
            start_date = original_start_date - timedelta(days=1)
            end_date = original_end_date + timedelta(days=1)

            print(f"📅 确定的时间范围:")
            print(f"  - 最早时间: {min_datetime}")
            print(f"  - 最晚时间: {max_datetime}")
            print(f"  - 原始日期范围: {original_start_date} 到 {original_end_date}")
            print(f"  - 扩展下载范围: {start_date} 到 {end_date} (前后各加1天)")
            print(f"  - 总天数: {(end_date - start_date).days + 1} 天")

            return start_date, end_date

        except Exception as e:
            print(f"❌ 分析Dune数据失败: {e}")
            raise

    def estimate_remaining_time(self, current_count, estimated_total, elapsed_time):
        """估算剩余下载时间"""
        if current_count == 0:
            return "计算中..."

        # 计算平均速度（消息/秒）
        avg_speed = current_count / elapsed_time

        # 估算剩余消息数
        remaining_messages = max(0, estimated_total - current_count)

        # 估算剩余时间
        if avg_speed > 0:
            remaining_seconds = remaining_messages / avg_speed
            return self.format_time_duration(remaining_seconds)
        else:
            return "未知"

    def format_time_duration(self, seconds):
        """格式化时间显示"""
        if seconds < 60:
            return f"{seconds:.0f}秒"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.1f}分钟"
        else:
            hours = seconds / 3600
            return f"{hours:.1f}小时"

    async def download_telegram_messages(self, start_date, end_date):
        """下载指定日期范围的群组消息"""
        try:
            print(f"\n📥 开始下载群组消息...")
            print(f"群组ID: {self.target_channel_id}")
            print(f"日期范围: {start_date} 到 {end_date}")

            # 获取群组实体
            channel = await self.telegram_client.get_entity(self.target_channel_id)
            channel_title = getattr(channel, 'title', str(self.target_channel_id))
            print(f"群组名称: {channel_title}")

            # 转换日期为datetime（包含完整的一天）
            start_datetime = datetime.combine(start_date, datetime.min.time())
            end_datetime = datetime.combine(end_date, datetime.max.time())

            messages = []
            message_count = 0
            start_time = time.time()

            # 使用成功验证的方法：从结束日期+1天开始往前获取
            offset_start_date = end_date + timedelta(days=1)
            offset_start_datetime = datetime.combine(offset_start_date, datetime.max.time())

            print(f"⏳ 开始下载消息...")
            print(f"🎯 从 {offset_start_date} 开始往前获取...")
            print(f"📊 进度显示：每100条消息更新一次")

            # 开始遍历消息
            print("🔄 开始遍历消息...")
            message_iter_count = 0  # 遍历的总消息数

            try:
                async for message in self.telegram_client.iter_messages(
                    channel,
                    offset_date=offset_start_datetime  # 从结束日期+1天开始，不设置limit限制
                ):
                    message_iter_count += 1
                    message_date = message.date.replace(tzinfo=None)

                    # 每1000条消息显示遍历进度
                    if message_iter_count % 1000 == 0:
                        print(f"  🔍 已遍历: {message_iter_count:,} 条消息，当前日期: {message_date.date()}")

                    # 检查是否超出时间范围
                    if message_date < start_datetime:
                        print(f"  ⏹️ 到达时间范围下限，停止下载。最后消息日期: {message_date.date()}")
                        break

                    if start_datetime <= message_date <= end_datetime:
                        # 转换消息为字典格式
                        try:
                            message_data = await self.convert_message_to_dict(message)
                            messages.append(message_data)
                            message_count += 1

                            # 显示详细进度（每100条消息）
                            if message_count % 100 == 0:
                                current_time = time.time()
                                elapsed = current_time - start_time

                                current_date = message_date.date()
                                try:
                                    elapsed_str = self.format_time_duration(elapsed)
                                except Exception as e:
                                    elapsed_str = f"{elapsed:.1f}秒"
                                    print(f"  ⚠️ 时间计算错误: {e}")

                                print(f"  📅 {current_date} | 已下载: {message_count:,} 条 | "
                                      f"用时: {elapsed_str}")

                            # 每10条消息显示简单进度
                            elif message_count % 10 == 0:
                                print(f"  📝 已下载: {message_count} 条消息，当前日期: {message_date.date()}")

                        except Exception as e:
                            print(f"  ⚠️ 处理消息 {message.id} 时出错: {e}")
                            continue

                    # 添加延迟避免API限制
                    if message_iter_count % 50 == 0:
                        await asyncio.sleep(0.5)  # 减少延迟时间

            except Exception as e:
                print(f"❌ 消息遍历过程中出错: {e}")
                import traceback
                traceback.print_exc()

            print(f"📊 遍历完成统计:")
            print(f"  - 总遍历消息数: {message_iter_count:,} 条")
            print(f"  - 符合条件消息数: {message_count:,} 条")

            total_time = time.time() - start_time
            print(f"\n✅ 下载完成!")
            print(f"📊 最终统计:")
            print(f"  - 总消息数: {len(messages):,} 条")
            print(f"  - 总用时: {self.format_time_duration(total_time)}")
            print(f"  - 平均速度: {len(messages)/total_time:.1f} 条/秒")

            return messages

        except Exception as e:
            print(f"❌ 下载消息时出错: {e}")
            import traceback
            traceback.print_exc()
            return []

    async def convert_message_to_dict(self, message):
        """将Telegram消息转换为字典格式（兼容telegram_to_excel.py）"""
        # 处理消息文本和实体 - 暂时简化处理，只保留纯文本
        text_data = []
        text_entities = []

        if message.text:
            # 暂时只保留纯文本，避免格式化解析错误
            text_data = [message.text]

        # 处理发送者信息
        from_info = None
        from_id = None
        if message.sender:
            from_info = getattr(message.sender, 'username', None) or getattr(message.sender, 'first_name', None)
            from_id = f"user{message.sender_id}"

        message_dict = {
            "id": message.id,
            "type": "message",
            "date": message.date.isoformat(),
            "date_unixtime": int(message.date.timestamp()),
            "from": from_info,
            "from_id": from_id,
            "text": text_data,
            "text_entities": text_entities,  # 保持兼容性，但主要信息在text数组中
            "forwarded_from": None,
            "reply_to_message_id": message.reply_to_msg_id,
            "media_type": None,
            "file": None,
            "thumbnail": None,
            "mime_type": None,
            "duration_seconds": None,
            "width": None,
            "height": None,
            "sticker_emoji": None
        }

        # 处理转发消息
        if message.forward:
            message_dict["forwarded_from"] = "forwarded"

        # 处理媒体
        if message.media:
            message_dict["media_type"] = type(message.media).__name__

        return message_dict

    def save_telegram_messages(self, messages, start_date, end_date):
        """保存Telegram消息到JSON文件"""
        if not messages:
            print("⚠️ 没有消息需要保存")
            return None

        # 构建输出数据结构（兼容telegram_to_excel.py）
        output_data = {
            "name": f"TokenGraduationGroup_{start_date}_{end_date}",
            "type": "supergroup",
            "id": abs(self.target_channel_id),
            "messages": messages
        }

        # 生成输出文件路径
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = config.get_file_path(
            f"telegram_graduation_messages_{timestamp}.json",
            config.DATA_DIR
        )

        # 保存到文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)

        # 生成统计报告
        print(f"\n📁 消息已保存到: {output_file}")
        print(f"📊 保存统计:")
        print(f"  - 群组ID: {self.target_channel_id}")
        print(f"  - 时间范围: {start_date} 到 {end_date}")
        print(f"  - 总消息数: {len(messages)}")
        print(f"  - 文件大小: {os.path.getsize(output_file) / 1024:.1f} KB")

        # 按日期统计消息数量
        daily_stats = {}
        for msg in messages:
            msg_date = datetime.fromisoformat(msg['date'].replace('Z', '+00:00')).date()
            daily_stats[msg_date] = daily_stats.get(msg_date, 0) + 1

        print(f"  - 每日消息统计:")
        for date, count in sorted(daily_stats.items()):
            print(f"    {date}: {count} 条")

        return output_file

    # === Telegram JSON转Excel功能 ===

    def process_telegram_json_to_excel(self, json_file_path):
        """处理Telegram JSON文件并转换为Excel"""
        print(f"📊 正在处理Telegram JSON文件: {json_file_path}")

        try:
            # 读取JSON文件
            with open(json_file_path, 'r', encoding='utf-8') as file:
                json_content = file.read()

            # 尝试解析JSON
            try:
                data = json.loads(json_content)
            except json.JSONDecodeError:
                # 尝试修复JSON格式
                fixed_content = json_content.strip()
                try:
                    data = json.loads(fixed_content)
                except json.JSONDecodeError:
                    print("❌ 无法解析JSON文件，请检查文件格式")
                    return None

            # 存储所有提取的数据
            all_extracted_data = []

            # 检查不同的JSON结构
            if isinstance(data, dict):
                # 检查是否有messages数组
                if "messages" in data and isinstance(data["messages"], list) and len(data["messages"]) > 0:
                    messages = data["messages"]
                    # 处理所有消息
                    processed_count = 0
                    for message in messages:
                        if "text" in message and isinstance(message["text"], list):
                            extracted_data = parse_telegram_message(message["text"])

                            # 确保Pro Traders字段存在
                            if "Pro Traders" not in extracted_data:
                                # 尝试在原始文本中查找
                                text_str = ""
                                for item in message["text"]:
                                    if isinstance(item, str):
                                        text_str += item
                                    elif isinstance(item, dict) and "text" in item:
                                        text_str += item["text"]

                                # 尝试匹配Pro Traders
                                pro_traders_match = re.search(r'Pro\s*Traders\s*:\s*(\d+)', text_str, re.IGNORECASE)
                                if pro_traders_match:
                                    extracted_data["Pro Traders"] = pro_traders_match.group(1)
                                else:
                                    # 设置默认值
                                    extracted_data["Pro Traders"] = 0

                            if extracted_data and len(extracted_data) > 0:
                                all_extracted_data.append(extracted_data)
                                processed_count += 1

                    print(f"  - 处理消息数: {len(messages)}")
                    print(f"  - 提取有效数据: {processed_count} 条")

                # 直接检查是否有text字段
                elif "text" in data and isinstance(data["text"], list):
                    extracted_data = parse_telegram_message(data["text"])

                    # 确保Pro Traders字段存在
                    if "Pro Traders" not in extracted_data:
                        # 尝试在原始文本中查找
                        text_str = ""
                        for item in data["text"]:
                            if isinstance(item, str):
                                text_str += item
                            elif isinstance(item, dict) and "text" in item:
                                text_str += item["text"]

                        # 尝试匹配Pro Traders
                        pro_traders_match = re.search(r'Pro\s*Traders\s*:\s*(\d+)', text_str, re.IGNORECASE)
                        if pro_traders_match:
                            extracted_data["Pro Traders"] = pro_traders_match.group(1)
                        else:
                            # 设置默认值
                            extracted_data["Pro Traders"] = 0

                    if extracted_data:
                        all_extracted_data.append(extracted_data)
            else:
                print("❌ 未找到有效的消息数据结构")
                return None

            if not all_extracted_data:
                print("⚠️ 未找到有效的消息数据")
                return None

            # 创建DataFrame
            df = pd.DataFrame(all_extracted_data)

            # 确保特定列存在
            required_columns = [
                "Pro Traders", "Top50状态", "24H换手率", "聪明钱", "开发者(DEV)",
                "老鼠仓", "老鼠仓2", "钓鱼地址", "阴谋集团", "巨鲸",
                "Buys", "Sells", "DexPaid支付时间", "跑路比例", "1m涨跌幅",
                # 猎人频道信息字段
                "买次数", "卖次数", "DEV占比", "老鼠钱包数量", "老鼠钱包token占比",
                "新钱包数量", "新钱包token占比", "总计聪明钱", "标签", "TGCall数量",
                "TGCaller名称", "粉丝数", "账号创建时间", "发布推文数", "平均浏览量",
                "平均评论数", "平均转推数", "平均点赞数",
                # 新增推特相关字段
                "CA关联推文", "推特喊单用户数", "推特caller名称", "推特订阅浏览量", "粉丝覆盖量"
            ]

            for col in required_columns:
                if col not in df.columns:
                    df[col] = None

            # 定义有效的列名列表
            valid_columns = [
                "代币地址", "名称", "符号", "描述", "创建者", "创建时间", "山丘之王时间戳",
                "进程1阶段", "回复数", "值定曲线", "历史最高市值", "Top 10 Holders",
                "Dev Holders", "Snipers Holders", "Insiders", "Holders", "Pro Traders",
                "Bundlers", "Buys", "Sells", "网站", "Twitter", "Telegram",
                "Insider Holding", "Dev Holding", "Bot Users", "DexPaid支付时间",
                "聪明钱", "KOL/VC", "开发者", "开发者(DEV)", "鲸鱼", "巨鲸", "新钱包",
                "狙击者", "持币大户", "老鼠仓", "老鼠仓2", "持有者数量", "老鼠仓百分比",
                "跑路百分比", "跑路比例", "Top 1 持有比例", "Top50状态", "24H换手率",
                "钓鱼地址", "阴谋集团", "1m涨跌幅", "检测时间",
                # 猎人频道信息字段
                "买次数", "卖次数", "DEV占比", "老鼠钱包数量", "老鼠钱包token占比",
                "新钱包数量", "新钱包token占比", "总计聪明钱", "标签", "TGCall数量",
                "TGCaller名称", "粉丝数", "账号创建时间", "发布推文数", "平均浏览量",
                "平均评论数", "平均转推数", "平均点赞数",
                # 新增推特相关字段
                "CA关联推文", "推特喊单用户数", "推特caller名称", "推特订阅浏览量", "粉丝覆盖量"
            ]

            # 只保留有效的列
            df = df[[col for col in valid_columns if col in df.columns]]

            # 新增功能：对网站、Twitter、Telegram字段进行编码
            print("\n🔧 开始字段编码处理...")
            df_with_encoding, encoding_stats = apply_field_encoding(df)

            # 更新有效列列表，包含新增的编码列
            updated_valid_columns = list(valid_columns)
            encoding_columns = ['网站_编码', 'Twitter_编码', 'Telegram_编码']
            for enc_col in encoding_columns:
                if enc_col in df_with_encoding.columns:
                    updated_valid_columns.append(enc_col)

            # 重新排序列，确保编码列紧跟在原始列后面
            final_columns = []
            encoding_columns_added = set()  # 跟踪已添加的编码列

            for col in valid_columns:  # 使用原始的valid_columns而不是updated_valid_columns
                if col in df_with_encoding.columns:
                    final_columns.append(col)
                    # 如果是原始的网站、Twitter、Telegram列，添加对应的编码列
                    if col == '网站' and '网站_编码' in df_with_encoding.columns and '网站_编码' not in encoding_columns_added:
                        final_columns.append('网站_编码')
                        encoding_columns_added.add('网站_编码')
                    elif col == 'Twitter' and 'Twitter_编码' in df_with_encoding.columns and 'Twitter_编码' not in encoding_columns_added:
                        final_columns.append('Twitter_编码')
                        encoding_columns_added.add('Twitter_编码')
                    elif col == 'Telegram' and 'Telegram_编码' in df_with_encoding.columns and 'Telegram_编码' not in encoding_columns_added:
                        final_columns.append('Telegram_编码')
                        encoding_columns_added.add('Telegram_编码')

            # 应用最终的列顺序
            df_final = df_with_encoding[final_columns]

            # 生成输出文件路径
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            excel_file_path = config.get_file_path(
                f"telegram_data_processed_{timestamp}.xlsx"
            )

            # 保存为Excel
            df_final.to_excel(excel_file_path, index=False)

            print(f"✅ Telegram数据已转换为Excel:")
            print(f"  - 输出文件: {excel_file_path}")
            print(f"  - 数据行数: {len(all_extracted_data)}")
            print(f"  - 数据列数: {len(df_final.columns)}")
            print(f"  - 原始字段列数: {len(df.columns)}")
            print(f"  - 新增编码列数: {len(df_final.columns) - len(df.columns)}")

            # 显示编码统计信息
            if encoding_stats:
                print(f"  - 字段编码统计:")
                for field, stats in encoding_stats.items():
                    print(f"    {field}: {stats}")

            return excel_file_path

        except Exception as e:
            print(f"❌ 处理Telegram JSON文件时出错: {e}")
            import traceback
            traceback.print_exc()
            return None

    # === 表格合并功能（来自保持格式合并表格.py）===

    def find_latest_files(self):
        """自动查找最新生成的Dune和Telegram Excel文件"""
        print("🔍 正在查找最新生成的文件...")

        # 查找最新的Dune Excel文件
        dune_pattern = os.path.join(config.OUTPUT_DIR, "dune_query_result_enhanced_*.xlsx")
        dune_files = glob.glob(dune_pattern)
        dune_file = None
        if dune_files:
            # 按修改时间排序，取最新的
            dune_file = max(dune_files, key=os.path.getmtime)
            print(f"  ✅ 找到Dune文件: {os.path.basename(dune_file)}")
        else:
            print(f"  ❌ 未找到Dune文件，搜索模式: {dune_pattern}")

        # 查找最新的Telegram Excel文件
        telegram_pattern = os.path.join(config.OUTPUT_DIR, "telegram_data_processed_*.xlsx")
        telegram_files = glob.glob(telegram_pattern)
        telegram_file = None
        if telegram_files:
            # 按修改时间排序，取最新的
            telegram_file = max(telegram_files, key=os.path.getmtime)
            print(f"  ✅ 找到Telegram文件: {os.path.basename(telegram_file)}")
        else:
            print(f"  ❌ 未找到Telegram文件，搜索模式: {telegram_pattern}")

        return dune_file, telegram_file

    def merge_excel_tables(self, dune_file, telegram_file):
        """合并Dune和Telegram Excel表格"""
        if not dune_file or not telegram_file:
            print("❌ 缺少必要的文件，无法进行合并")
            return None

        print(f"📊 开始合并表格...")
        print(f"  - Dune文件: {os.path.basename(dune_file)}")
        print(f"  - Telegram文件: {os.path.basename(telegram_file)}")

        try:
            # 导入openpyxl（用于保持格式）
            try:
                import openpyxl
            except ImportError:
                print("❌ 需要安装openpyxl库: pip install openpyxl")
                return None

            # 读取Dune文件（作为主表格，保持格式）
            print("📖 读取Dune表格...")
            dune_workbook = openpyxl.load_workbook(dune_file)
            dune_sheet = dune_workbook.active

            # 读取Telegram文件（作为数据源）
            print("📖 读取Telegram表格...")
            telegram_df = pd.read_excel(telegram_file)

            # 获取列名
            dune_headers = [cell.value for cell in dune_sheet[1]]
            telegram_headers = list(telegram_df.columns)

            print(f"  - Dune表格列数: {len([h for h in dune_headers if h])}")
            print(f"  - Telegram表格列数: {len(telegram_headers)}")

            # 确定匹配列
            dune_match_col = config.MERGE_CONFIG['dune_match_column']
            telegram_match_col = config.MERGE_CONFIG['telegram_match_column']

            # 验证匹配列是否存在
            if dune_match_col not in dune_headers:
                print(f"❌ Dune表格中未找到匹配列: {dune_match_col}")
                print(f"   可用列: {[h for h in dune_headers if h]}")
                return None

            if telegram_match_col not in telegram_headers:
                print(f"❌ Telegram表格中未找到匹配列: {telegram_match_col}")
                print(f"   可用列: {telegram_headers}")
                return None

            print(f"✅ 匹配列确认:")
            print(f"  - Dune表格匹配列: {dune_match_col}")
            print(f"  - Telegram表格匹配列: {telegram_match_col}")

            # 获取匹配列索引
            dune_match_col_idx = dune_headers.index(dune_match_col) + 1  # Excel列从1开始

            # 获取需要添加的列（排除匹配列）
            add_cols = [col for col in telegram_headers if col != telegram_match_col]
            print(f"📋 将添加 {len(add_cols)} 列到Dune表格")

            # 为Dune表格添加新列标题
            for col in add_cols:
                if col not in dune_headers:  # 如果列不存在，则添加
                    dune_sheet.cell(row=1, column=len(dune_headers) + 1).value = col
                    dune_headers.append(col)

            # 创建Telegram数据的匹配映射
            telegram_match_map = {}
            for idx, row in telegram_df.iterrows():
                match_value = row[telegram_match_col]
                if pd.notna(match_value):
                    telegram_match_map[str(match_value)] = row

            print(f"📊 Telegram数据映射: {len(telegram_match_map)} 条记录")

            # 合并数据
            matched_count = 0
            for row_idx in range(2, dune_sheet.max_row + 1):  # 从第2行开始（跳过标题）
                dune_match_value = dune_sheet.cell(row=row_idx, column=dune_match_col_idx).value

                if dune_match_value is not None and str(dune_match_value) in telegram_match_map:
                    telegram_row = telegram_match_map[str(dune_match_value)]
                    matched_count += 1

                    # 复制Telegram数据到Dune表格
                    for col_name in add_cols:
                        if col_name in dune_headers:
                            col_idx = dune_headers.index(col_name) + 1
                            value = telegram_row[col_name]
                            # 处理NaN值
                            if pd.isna(value):
                                value = None
                            dune_sheet.cell(row=row_idx, column=col_idx).value = value

            print(f"✅ 合并完成: {matched_count} 行数据成功匹配")
            print(f"📊 合并后表格大小: {dune_sheet.max_row} 行 × {dune_sheet.max_column} 列")

            # 生成输出文件路径
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = config.get_file_path(
                f"{config.MERGE_CONFIG['merge_output_prefix']}_{timestamp}.xlsx"
            )

            # 保存合并结果
            print(f"💾 保存合并结果到: {os.path.basename(output_file)}")
            dune_workbook.save(output_file)

            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                print(f"✅ 合并文件保存成功:")
                print(f"  - 文件路径: {output_file}")
                print(f"  - 文件大小: {file_size:,} 字节")
                print(f"  - 匹配记录: {matched_count} 条")
                return output_file
            else:
                print("❌ 文件保存失败")
                return None

        except Exception as e:
            print(f"❌ 合并过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return None

    # === 集成处理流程 ===

    async def run_complete_process(self):
        """运行完整的集成数据处理流程"""
        print("🚀 开始集成数据处理流程")
        print("=" * 60)

        # 显示代理状态
        self.print_proxy_status()

        try:
            # 第一步：下载和处理Dune数据
            print("\n📊 第一步：处理Dune数据")
            print("-" * 40)

            dune_data = self.download_dune_data()
            enhanced_dune_data = self.calculate_time_differences(dune_data)
            self.show_dune_sample_data(enhanced_dune_data)
            dune_file_paths = self.save_dune_results(enhanced_dune_data)

            # 第二步：分析时间范围并下载Telegram消息
            print("\n📱 第二步：处理Telegram数据")
            print("-" * 40)

            start_date, end_date = self.analyze_dune_timerange(enhanced_dune_data)

            # 连接Telegram
            await self.connect_telegram()

            try:
                # 下载消息
                messages = await self.download_telegram_messages(start_date, end_date)

                # 保存消息
                telegram_json_path = self.save_telegram_messages(messages, start_date, end_date)

            finally:
                # 确保断开连接
                await self.disconnect_telegram()

            # 第三步：处理Telegram JSON转Excel
            print("\n📊 第三步：转换Telegram数据为Excel")
            print("-" * 40)

            telegram_excel_path = None
            if telegram_json_path:
                telegram_excel_path = self.process_telegram_json_to_excel(telegram_json_path)

            # 第四步：自动合并表格
            print("\n🔗 第四步：自动合并表格")
            print("-" * 40)

            merged_file_path = None
            if config.MERGE_CONFIG['auto_merge_enabled'] and telegram_excel_path:
                # 自动查找并合并最新的文件
                dune_file, telegram_file = self.find_latest_files()
                if dune_file and telegram_file:
                    merged_file_path = self.merge_excel_tables(dune_file, telegram_file)
                else:
                    print("⚠️ 未找到合适的文件进行合并")
            else:
                print("⚠️ 自动合并已禁用或Telegram数据不可用")

            # 第五步：生成最终报告
            print("\n📋 第五步：生成处理报告")
            print("-" * 40)

            result = {
                'success': True,
                'dune_data': {
                    'rows': len(enhanced_dune_data),
                    'columns': len(enhanced_dune_data.columns),
                    'files': dune_file_paths
                },
                'telegram_data': {
                    'messages': len(messages) if messages else 0,
                    'date_range': f"{start_date} 到 {end_date}",
                    'json_file': telegram_json_path,
                    'excel_file': telegram_excel_path
                },
                'merged_data': {
                    'file': merged_file_path,
                    'enabled': config.MERGE_CONFIG['auto_merge_enabled']
                },
                'proxy_enabled': self.proxy_enabled
            }

            self.print_final_report(result)
            return result

        except Exception as e:
            print(f"\n❌ 集成处理过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': str(e)
            }

    def print_final_report(self, result):
        """打印最终处理报告"""
        print("\n🎉 集成数据处理完成！")
        print("=" * 60)

        if result['success']:
            print("✅ 处理状态: 成功")
            print(f"🌐 代理状态: {'已启用' if result['proxy_enabled'] else '已禁用'}")

            print(f"\n📊 Dune数据处理结果:")
            print(f"  - 数据行数: {result['dune_data']['rows']:,}")
            print(f"  - 数据列数: {result['dune_data']['columns']}")
            print(f"  - CSV文件: {result['dune_data']['files']['csv']}")
            print(f"  - Excel文件: {result['dune_data']['files']['excel']}")

            print(f"\n📱 Telegram数据处理结果:")
            print(f"  - 消息数量: {result['telegram_data']['messages']:,}")
            print(f"  - 时间范围: {result['telegram_data']['date_range']}")
            print(f"  - JSON文件: {result['telegram_data']['json_file']}")
            if result['telegram_data']['excel_file']:
                print(f"  - Excel文件: {result['telegram_data']['excel_file']}")
            else:
                print(f"  - Excel文件: 未生成（无有效数据）")

            print(f"\n🔗 表格合并结果:")
            if result['merged_data']['enabled']:
                if result['merged_data']['file']:
                    print(f"  ✅ 自动合并成功: {os.path.basename(result['merged_data']['file'])}")
                    print(f"  📁 合并文件路径: {result['merged_data']['file']}")
                else:
                    print(f"  ❌ 自动合并失败或未找到合适的文件")
            else:
                print(f"  ⚠️ 自动合并功能已禁用")

            print(f"\n💡 下一步建议:")
            if result['merged_data']['file']:
                print(f"  1. ✅ 数据已完全处理并合并完成")
                print(f"  2. 可以直接使用合并后的Excel文件进行分析")
                print(f"  3. 合并文件包含Dune和Telegram的完整数据")
            elif result['telegram_data']['excel_file']:
                print(f"  1. ✅ Telegram数据已自动转换为Excel格式")
                print(f"  2. 如需要，可手动运行表格合并功能")
                print(f"  3. 继续后续的数据分析流程")
            else:
                print(f"  1. 检查Telegram消息是否包含有效的代币数据")
                print(f"  2. 如需要，可手动运行 telegram_to_excel.py 处理JSON文件")
                print(f"  3. 使用表格合并脚本合并数据")
        else:
            print("❌ 处理状态: 失败")
            print(f"错误信息: {result['error']}")

        print("=" * 60)


# === 主函数和命令行接口 ===

async def main():
    """主函数"""
    print("🔧 集成数据处理器")
    print("整合Dune数据下载和Telegram消息自动下载功能")
    print("=" * 60)

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='集成数据处理器')
    parser.add_argument('--enable-proxy', action='store_true',
                       help='启用代理（覆盖配置文件设置）')
    parser.add_argument('--disable-proxy', action='store_true',
                       help='禁用代理（覆盖配置文件设置）')
    parser.add_argument('--proxy-url', type=str,
                       help='指定代理URL（覆盖配置文件设置）')

    try:
        args = parser.parse_args()

        # 创建处理器实例
        processor = IntegratedDataProcessor()

        # 根据命令行参数调整代理设置
        if args.enable_proxy:
            processor.proxy_enabled = True
            print("🌐 命令行参数：启用代理")
        elif args.disable_proxy:
            processor.proxy_enabled = False
            print("🌐 命令行参数：禁用代理")

        if args.proxy_url:
            processor.proxy_url = args.proxy_url
            print(f"🌐 命令行参数：使用代理URL {args.proxy_url}")

        # 重新初始化客户端（应用新的代理设置）
        processor._init_clients()

        # 运行完整处理流程
        result = await processor.run_complete_process()

        # 根据结果设置退出码
        sys.exit(0 if result['success'] else 1)

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())