#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel表格合并工具
功能：
- 交互式合并两个Excel表格
- 支持LEFT JOIN操作
- 基于指定列进行匹配
- 将表B的指定列合并到主表A中
- 直接覆盖原主表A文件
"""

import pandas as pd
import numpy as np
import os
import sys
from pathlib import Path
import re
from datetime import datetime

class ExcelMergeTool:
    def __init__(self):
        """
        初始化Excel合并工具
        """
        self.excel_file_path = None
        self.main_sheet_name = None
        self.merge_sheet_name = None
        self.main_table_data = None
        self.merge_table_data = None
        self.main_match_column = None
        self.merge_match_column = None
        self.merge_columns = []
        self.result_data = None
        self.available_sheets = []

    def column_letter_to_index(self, letter):
        """
        将列字母转换为索引（A=0, B=1, C=2, ...）

        Args:
            letter (str): 列字母（如A, B, C等）

        Returns:
            int: 列索引
        """
        letter = letter.upper().strip()
        if not letter.isalpha():
            raise ValueError(f"无效的列字母: {letter}")

        result = 0
        for char in letter:
            result = result * 26 + (ord(char) - ord('A') + 1)
        return result - 1

    def index_to_column_letter(self, index):
        """
        将列索引转换为字母（0=A, 1=B, 2=C, ...）

        Args:
            index (int): 列索引

        Returns:
            str: 列字母
        """
        result = ""
        while index >= 0:
            result = chr(index % 26 + ord('A')) + result
            index = index // 26 - 1
        return result

    def validate_column_letter(self, letter, max_columns):
        """
        验证列字母是否有效

        Args:
            letter (str): 列字母
            max_columns (int): 最大列数

        Returns:
            bool: 是否有效
        """
        try:
            index = self.column_letter_to_index(letter)
            return 0 <= index < max_columns
        except:
            return False

    def get_available_sheets(self, file_path):
        """
        获取Excel文件中所有可用的工作表

        Args:
            file_path (str): Excel文件路径

        Returns:
            list: 工作表名称列表，失败返回None
        """
        try:
            print(f"📊 正在读取工作簿: {file_path}")
            excel_file = pd.ExcelFile(file_path)
            sheets = excel_file.sheet_names
            print(f"✅ 工作簿读取成功，共发现 {len(sheets)} 个工作表")

            # 显示工作表列表
            print("📋 可用工作表:")
            for i, sheet in enumerate(sheets, 1):
                print(f"  {i}. {sheet}")

            return sheets

        except Exception as e:
            print(f"❌ 读取工作簿失败: {e}")
            return None

    def load_excel_sheet(self, file_path, sheet_name, table_name):
        """
        加载Excel文件的指定工作表

        Args:
            file_path (str): 文件路径
            sheet_name (str): 工作表名称
            table_name (str): 表格名称（用于显示）

        Returns:
            pd.DataFrame: 加载的数据，失败返回None
        """
        try:
            print(f"📊 正在加载{table_name}工作表: '{sheet_name}'")
            data = pd.read_excel(file_path, sheet_name=sheet_name)
            print(f"✅ {table_name}加载成功，共 {len(data)} 行，{len(data.columns)} 列")

            # 显示列信息
            print(f"📋 {table_name}列信息:")
            for i, col in enumerate(data.columns):
                letter = self.index_to_column_letter(i)
                print(f"  {letter}: {col}")

            return data

        except Exception as e:
            print(f"❌ 加载{table_name}失败: {e}")
            return None

    def get_file_path_input(self, prompt):
        """
        获取文件路径输入

        Args:
            prompt (str): 输入提示

        Returns:
            str: 文件路径，用户取消返回None
        """
        while True:
            print(f"\n{prompt}")
            print("提示：支持带引号和不带引号的路径")
            print("示例：data.xlsx 或 \"C:\\path\\to\\data.xlsx\"")
            print("输入 'q' 或 'quit' 退出程序")

            input_path = input(">>> ").strip()

            if input_path.lower() in ['q', 'quit']:
                print("👋 程序已退出")
                return None

            if not input_path:
                print("❌ 请输入有效的文件路径")
                continue

            # 处理带引号的路径
            if (input_path.startswith('"') and input_path.endswith('"')) or \
               (input_path.startswith("'") and input_path.endswith("'")):
                input_path = input_path[1:-1]

            file_path = Path(input_path)

            if not file_path.exists():
                print(f"❌ 文件不存在: {file_path}")
                print("请检查文件路径是否正确")
                continue

            if not file_path.suffix.lower() in ['.xlsx', '.xls']:
                print("❌ 请选择Excel文件（.xlsx 或 .xls 格式）")
                continue

            print(f"✅ 文件找到: {file_path}")
            return str(file_path)

    def get_sheet_selection(self, prompt, sheets):
        """
        获取工作表选择

        Args:
            prompt (str): 输入提示
            sheets (list): 可用工作表列表

        Returns:
            str: 选择的工作表名称，用户取消返回None
        """
        while True:
            print(f"\n{prompt}")
            print("📋 可用工作表:")
            for i, sheet in enumerate(sheets, 1):
                print(f"  {i}. {sheet}")
            print("请输入工作表编号或直接输入工作表名称")
            print("输入 'q' 或 'quit' 退出程序")

            user_input = input(">>> ").strip()

            if user_input.lower() in ['q', 'quit']:
                print("👋 程序已退出")
                return None

            if not user_input:
                print("❌ 请输入有效的工作表编号或名称")
                continue

            # 尝试按编号选择
            if user_input.isdigit():
                sheet_index = int(user_input) - 1
                if 0 <= sheet_index < len(sheets):
                    selected_sheet = sheets[sheet_index]
                    print(f"✅ 选择工作表: {selected_sheet}")
                    return selected_sheet
                else:
                    print(f"❌ 无效的编号: {user_input}")
                    print(f"请输入1到{len(sheets)}之间的数字")
                    continue

            # 尝试按名称选择
            if user_input in sheets:
                print(f"✅ 选择工作表: {user_input}")
                return user_input
            else:
                print(f"❌ 未找到工作表: {user_input}")
                print("请检查工作表名称是否正确")
                continue

    def get_column_input(self, prompt, data, table_name):
        """
        获取列输入

        Args:
            prompt (str): 输入提示
            data (pd.DataFrame): 数据表
            table_name (str): 表格名称

        Returns:
            int: 列索引，用户取消返回None
        """
        max_columns = len(data.columns)

        while True:
            print(f"\n{prompt}")
            print(f"📋 {table_name}可用列:")
            for i, col in enumerate(data.columns):
                letter = self.index_to_column_letter(i)
                print(f"  {letter}: {col}")
            print("输入 'q' 或 'quit' 退出程序")

            input_col = input(">>> ").strip()

            if input_col.lower() in ['q', 'quit']:
                print("👋 程序已退出")
                return None

            if not input_col:
                print("❌ 请输入有效的列字母")
                continue

            if not self.validate_column_letter(input_col, max_columns):
                print(f"❌ 无效的列字母: {input_col}")
                print(f"请输入A到{self.index_to_column_letter(max_columns-1)}之间的字母")
                continue

            col_index = self.column_letter_to_index(input_col)
            col_name = data.columns[col_index]
            print(f"✅ 选择列: {input_col} ({col_name})")
            return col_index

    def get_multiple_columns_input(self, prompt, data, table_name):
        """
        获取多列输入

        Args:
            prompt (str): 输入提示
            data (pd.DataFrame): 数据表
            table_name (str): 表格名称

        Returns:
            list: 列索引列表，用户取消返回None
        """
        max_columns = len(data.columns)

        while True:
            print(f"\n{prompt}")
            print(f"📋 {table_name}可用列:")
            for i, col in enumerate(data.columns):
                letter = self.index_to_column_letter(i)
                print(f"  {letter}: {col}")
            print("请输入多个列字母，用逗号分隔（如：E,F,G）")
            print("输入 'q' 或 'quit' 退出程序")

            input_cols = input(">>> ").strip()

            if input_cols.lower() in ['q', 'quit']:
                print("👋 程序已退出")
                return None

            if not input_cols:
                print("❌ 请输入有效的列字母")
                continue

            # 分割并清理列字母
            col_letters = [col.strip() for col in input_cols.split(',')]
            col_indices = []
            invalid_cols = []

            for col_letter in col_letters:
                if not col_letter:
                    continue

                if not self.validate_column_letter(col_letter, max_columns):
                    invalid_cols.append(col_letter)
                else:
                    col_index = self.column_letter_to_index(col_letter)
                    col_indices.append(col_index)

            if invalid_cols:
                print(f"❌ 无效的列字母: {', '.join(invalid_cols)}")
                print(f"请输入A到{self.index_to_column_letter(max_columns-1)}之间的字母")
                continue

            if not col_indices:
                print("❌ 请至少选择一列")
                continue

            # 显示选择的列
            selected_cols = []
            for col_index in col_indices:
                col_letter = self.index_to_column_letter(col_index)
                col_name = data.columns[col_index]
                selected_cols.append(f"{col_letter} ({col_name})")

            print(f"✅ 选择的列: {', '.join(selected_cols)}")
            return col_indices

    def perform_merge(self):
        """
        执行表格合并操作

        Returns:
            bool: 合并是否成功
        """
        try:
            print("\n" + "="*50)
            print("🔄 开始执行表格合并...")
            print("="*50)

            # 获取列名
            main_col_name = self.main_table_data.columns[self.main_match_column]
            merge_col_name = self.merge_table_data.columns[self.merge_match_column]

            print(f"📊 合并配置:")
            print(f"  主表匹配列: {self.index_to_column_letter(self.main_match_column)} ({main_col_name})")
            print(f"  被合并表匹配列: {self.index_to_column_letter(self.merge_match_column)} ({merge_col_name})")

            merge_col_names = []
            for col_index in self.merge_columns:
                col_letter = self.index_to_column_letter(col_index)
                col_name = self.merge_table_data.columns[col_index]
                merge_col_names.append(f"{col_letter} ({col_name})")
            print(f"  要合并的列: {', '.join(merge_col_names)}")

            # 准备合并数据
            print("\n🔍 正在准备合并数据...")

            # 创建主表副本
            self.result_data = self.main_table_data.copy()

            # 准备被合并表的数据（只包含匹配列和要合并的列）
            merge_columns_to_use = [self.merge_match_column] + self.merge_columns
            merge_data_subset = self.merge_table_data.iloc[:, merge_columns_to_use].copy()

            # 重命名列以避免冲突
            new_column_names = {}
            for col_index in self.merge_columns:
                original_name = self.merge_table_data.columns[col_index]
                # 如果列名已存在，添加后缀
                new_name = original_name
                suffix = 1
                while new_name in self.result_data.columns:
                    new_name = f"{original_name}_{suffix}"
                    suffix += 1
                new_column_names[original_name] = new_name

            # 应用列名重命名
            if new_column_names:
                merge_data_subset = merge_data_subset.rename(columns=new_column_names)
                print(f"📝 列名重命名: {new_column_names}")

            # 执行LEFT JOIN
            print("🔗 正在执行LEFT JOIN合并...")

            # 获取匹配列名
            main_match_col_name = self.main_table_data.columns[self.main_match_column]
            merge_match_col_name = self.merge_table_data.columns[self.merge_match_column]

            # 执行合并
            self.result_data = pd.merge(
                self.result_data,
                merge_data_subset,
                left_on=main_match_col_name,
                right_on=merge_match_col_name,
                how='left',
                suffixes=('', '_merge')
            )

            # 删除重复的匹配列（如果存在）
            duplicate_match_col = f"{merge_match_col_name}_merge"
            if duplicate_match_col in self.result_data.columns:
                self.result_data = self.result_data.drop(columns=[duplicate_match_col])

            print("✅ 表格合并完成")

            # 显示合并统计
            self.print_merge_statistics()

            return True

        except Exception as e:
            print(f"❌ 表格合并失败: {e}")
            return False

    def print_merge_statistics(self):
        """
        打印合并统计信息
        """
        print("\n" + "="*50)
        print("📊 合并统计信息")
        print("="*50)

        # 基本统计
        original_rows = len(self.main_table_data)
        original_cols = len(self.main_table_data.columns)
        result_rows = len(self.result_data)
        result_cols = len(self.result_data.columns)
        added_cols = result_cols - original_cols

        print(f"原主表: {original_rows} 行 × {original_cols} 列")
        print(f"合并后: {result_rows} 行 × {result_cols} 列")
        print(f"新增列数: {added_cols}")

        # 匹配统计
        main_match_col = self.main_table_data.columns[self.main_match_column]
        merge_match_col = self.merge_table_data.columns[self.merge_match_column]

        # 计算匹配情况
        main_unique_values = set(self.main_table_data[main_match_col].dropna().astype(str))
        merge_unique_values = set(self.merge_table_data[merge_match_col].dropna().astype(str))

        matched_values = main_unique_values.intersection(merge_unique_values)
        unmatched_main = main_unique_values - merge_unique_values

        print(f"\n🔍 匹配情况分析:")
        print(f"主表唯一匹配值: {len(main_unique_values)}")
        print(f"被合并表唯一匹配值: {len(merge_unique_values)}")
        print(f"成功匹配的值: {len(matched_values)}")
        print(f"主表中未匹配的值: {len(unmatched_main)}")

        # 计算有数据的行数（新增列中）
        added_column_names = []
        for col_index in self.merge_columns:
            original_name = self.merge_table_data.columns[col_index]
            # 找到实际添加的列名
            for col in self.result_data.columns:
                if col == original_name or col.startswith(f"{original_name}_"):
                    if col not in self.main_table_data.columns:
                        added_column_names.append(col)
                        break

        if added_column_names:
            print(f"\n📈 新增列数据填充情况:")
            for col_name in added_column_names:
                non_null_count = self.result_data[col_name].notna().sum()
                fill_rate = (non_null_count / len(self.result_data)) * 100
                print(f"  {col_name}: {non_null_count}/{len(self.result_data)} 行有数据 ({fill_rate:.1f}%)")

    def save_result(self):
        """
        保存合并结果到原Excel文件的主表工作表

        Returns:
            bool: 保存是否成功
        """
        try:
            print(f"\n💾 正在保存结果到: {self.excel_file_path}")
            print(f"📋 更新工作表: '{self.main_sheet_name}'")

            # 检查文件是否被占用
            try:
                # 尝试以写入模式打开文件来检查是否被占用
                with open(self.excel_file_path, 'r+b'):
                    pass
            except PermissionError:
                print("❌ 文件被占用！请关闭Excel或其他正在使用该文件的程序后重试")
                print(f"文件路径: {self.excel_file_path}")
                return False

            # 读取原Excel文件的所有工作表
            print("📊 正在读取原工作簿的所有工作表...")
            with pd.ExcelFile(self.excel_file_path) as excel_file:
                all_sheets = {}
                for sheet_name in excel_file.sheet_names:
                    if sheet_name == self.main_sheet_name:
                        # 使用合并后的数据替换主表工作表
                        all_sheets[sheet_name] = self.result_data
                        print(f"📋 准备更新工作表: '{sheet_name}'")
                    else:
                        # 保持其他工作表不变
                        all_sheets[sheet_name] = pd.read_excel(excel_file, sheet_name=sheet_name)
                        print(f"📋 保持工作表不变: '{sheet_name}'")

            # 保存所有工作表到文件
            print("💾 正在保存更新后的工作簿...")
            with pd.ExcelWriter(self.excel_file_path, engine='openpyxl', mode='w') as writer:
                for sheet_name, data in all_sheets.items():
                    data.to_excel(writer, sheet_name=sheet_name, index=False)

            print("✅ 结果保存成功")
            print(f"📁 文件已更新: {self.excel_file_path}")
            print(f"📋 更新的工作表: '{self.main_sheet_name}'")
            print("📋 其他工作表保持不变")

            return True

        except PermissionError as e:
            print(f"❌ 文件权限错误: {e}")
            print("💡 解决方案:")
            print("  1. 关闭Excel或其他正在使用该文件的程序")
            print("  2. 检查文件是否为只读状态")
            print("  3. 确保有足够的磁盘空间")
            return False
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")
            print("💡 可能的原因:")
            print("  1. 文件被其他程序占用")
            print("  2. 磁盘空间不足")
            print("  3. 文件路径包含特殊字符")
            return False

    def run_interactive_merge(self):
        """
        运行交互式合并流程
        """
        print("\n" + "="*60)
        print("🎯 Excel工作表合并工具")
        print("="*60)
        print("功能：在同一个Excel工作簿内合并不同工作表")
        print("方式：LEFT JOIN操作，保持主表工作表的所有行")
        print("输出：直接更新原Excel文件的主表工作表")
        print("="*60)

        try:
            # 1. 获取Excel工作簿路径
            self.excel_file_path = self.get_file_path_input("📁 请输入Excel工作簿文件路径:")
            if self.excel_file_path is None:
                return

            # 2. 获取可用工作表列表
            self.available_sheets = self.get_available_sheets(self.excel_file_path)
            if self.available_sheets is None or len(self.available_sheets) < 2:
                print("❌ 工作簿中至少需要2个工作表才能进行合并")
                return

            # 3. 选择主表工作表（表A）
            self.main_sheet_name = self.get_sheet_selection(
                "📋 请选择主表工作表（表A）:",
                self.available_sheets
            )
            if self.main_sheet_name is None:
                return

            # 4. 选择被合并工作表（表B）
            remaining_sheets = [sheet for sheet in self.available_sheets if sheet != self.main_sheet_name]
            if not remaining_sheets:
                print("❌ 没有其他工作表可供选择")
                return

            self.merge_sheet_name = self.get_sheet_selection(
                "📋 请选择被合并工作表（表B）:",
                remaining_sheets
            )
            if self.merge_sheet_name is None:
                return

            # 5. 加载主表工作表数据
            self.main_table_data = self.load_excel_sheet(
                self.excel_file_path,
                self.main_sheet_name,
                "主表A"
            )
            if self.main_table_data is None:
                return

            # 6. 加载被合并工作表数据
            self.merge_table_data = self.load_excel_sheet(
                self.excel_file_path,
                self.merge_sheet_name,
                "被合并表B"
            )
            if self.merge_table_data is None:
                return

            # 7. 选择主表A的匹配列
            self.main_match_column = self.get_column_input(
                "🔗 请选择主表A的匹配列（用于JOIN操作）:",
                self.main_table_data,
                "主表A"
            )
            if self.main_match_column is None:
                return

            # 8. 选择被合并表B的匹配列
            self.merge_match_column = self.get_column_input(
                "🔗 请选择被合并表B的匹配列（用于JOIN操作）:",
                self.merge_table_data,
                "被合并表B"
            )
            if self.merge_match_column is None:
                return

            # 9. 选择要合并的列
            self.merge_columns = self.get_multiple_columns_input(
                "📋 请选择被合并表B中要合并到主表A的列:",
                self.merge_table_data,
                "被合并表B"
            )
            if self.merge_columns is None:
                return

            # 10. 执行合并
            if not self.perform_merge():
                return

            # 11. 确认保存
            print("\n" + "="*50)
            print("⚠️  注意：即将更新原Excel文件的主表工作表")
            print(f"📋 将更新工作表: '{self.main_sheet_name}'")
            print("其他工作表保持不变")
            print("="*50)

            while True:
                confirm = input("是否确认保存合并结果？(y/n): ").strip().lower()
                if confirm in ['y', 'yes', '是']:
                    break
                elif confirm in ['n', 'no', '否']:
                    print("❌ 用户取消保存操作")
                    return
                else:
                    print("请输入 y 或 n")

            # 12. 保存结果
            if self.save_result():
                print("\n🎉 Excel工作表合并完成！")
                print(f"✅ 工作表 '{self.main_sheet_name}' 已更新")
            else:
                print("❌ 保存失败，请检查文件权限")

        except KeyboardInterrupt:
            print("\n\n👋 用户中断操作，程序退出")
        except Exception as e:
            print(f"\n❌ 程序执行出错: {e}")


def main():
    """
    主函数
    """
    try:
        merger = ExcelMergeTool()
        merger.run_interactive_merge()
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
