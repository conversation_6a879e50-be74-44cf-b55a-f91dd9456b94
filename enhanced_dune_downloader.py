# -*- coding: utf-8 -*-
"""
增强版Dune数据下载器
整合了原始Dune API查询功能和时间差计算功能

功能：
1. 从Dune API获取查询结果
2. 自动计算ath_time - added_time的时间差（秒）
3. 自动计算atl_time - added_time的时间差（秒）
4. 保存为CSV和Excel格式
5. 提供详细的处理报告

使用方法：
1. 设置您的Dune API密钥和查询ID
2. 运行脚本
3. 查看生成的增强版数据文件
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
import sys

# 导入配置
from config import config

# 尝试导入Dune客户端
try:
    from dune_client.types import QueryParameter
    from dune_client.client import DuneClient
    from dune_client.query import QueryBase
    DUNE_AVAILABLE = True
except ImportError:
    print("警告：未安装dune_client库，请运行: pip install dune_client")
    DUNE_AVAILABLE = False

class EnhancedDuneDownloader:
    def __init__(self, api_key=None, query_id=None):
        """
        初始化增强版Dune下载器

        Args:
            api_key (str, optional): Dune API密钥，默认从配置文件读取
            query_id (int, optional): Dune查询ID，默认从配置文件读取
        """
        self.api_key = api_key or config.DUNE_API_KEY
        self.query_id = query_id or config.DUNE_QUERY_ID
        self.client = None

        if DUNE_AVAILABLE:
            self.client = DuneClient(
                api_key=self.api_key,
                base_url="https://api.dune.com",
                request_timeout=config.DUNE_REQUEST_TIMEOUT
            )

    def download_data(self):
        """
        从Dune API下载数据

        Returns:
            pd.DataFrame: 查询结果数据框
        """
        if not DUNE_AVAILABLE:
            raise ImportError("Dune客户端不可用，请安装dune_client库")

        print(f"正在从Dune API获取查询 {self.query_id} 的数据...")

        try:
            query_result = self.client.get_latest_result_dataframe(query=self.query_id)
            print(f"成功获取数据，共 {len(query_result)} 行，{len(query_result.columns)} 列")
            return query_result
        except Exception as e:
            print(f"获取Dune数据时出错: {e}")
            raise

    def calculate_time_differences(self, df):
        """
        计算时间差值并添加新列

        Args:
            df (pd.DataFrame): 包含时间列的DataFrame

        Returns:
            pd.DataFrame: 添加了时间差列的DataFrame
        """
        print("\n开始计算时间差...")

        # 检查必需的列是否存在
        required_columns = config.TIME_DIFF_CONFIG['required_columns']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            print(f"警告：缺少以下列，将跳过时间差计算: {missing_columns}")
            print(f"可用列: {list(df.columns)}")
            return df

        # 创建副本以避免修改原始数据
        df_copy = df.copy()

        # 转换时间列为datetime格式
        def parse_time_column(series, col_name):
            """解析时间列，支持多种格式"""
            try:
                # 尝试直接转换
                parsed = pd.to_datetime(series, errors='coerce')
                valid_count = parsed.notna().sum()
                print(f"  {col_name}: 成功解析 {valid_count}/{len(series)} 个时间值")
                return parsed
            except Exception as e:
                print(f"  转换{col_name}列时出错: {e}")
                return series

        # 转换时间列
        print("正在解析时间列...")
        df_copy['ath_time_parsed'] = parse_time_column(df_copy['ath_time'], 'ath_time')
        df_copy['atl_time_parsed'] = parse_time_column(df_copy['atl_time'], 'atl_time')
        df_copy['added_time_parsed'] = parse_time_column(df_copy['added_time'], 'added_time')

        # 计算时间差（秒）
        def calculate_seconds_diff(time1, time2):
            """计算两个时间之间的秒数差值"""
            try:
                if pd.isna(time1) or pd.isna(time2):
                    return np.nan
                diff = (time1 - time2).total_seconds()
                return int(diff)  # 返回整数秒
            except Exception:
                return np.nan

        print("正在计算时间差...")

        # 计算ath_time - added_time的时间差
        df_copy['ath_time_diff_seconds'] = df_copy.apply(
            lambda row: calculate_seconds_diff(row['ath_time_parsed'], row['added_time_parsed']),
            axis=1
        )

        # 计算atl_time - added_time的时间差
        df_copy['atl_time_diff_seconds'] = df_copy.apply(
            lambda row: calculate_seconds_diff(row['atl_time_parsed'], row['added_time_parsed']),
            axis=1
        )

        # 删除临时的解析列
        df_copy = df_copy.drop(['ath_time_parsed', 'atl_time_parsed', 'added_time_parsed'], axis=1)

        # 统计计算结果
        ath_valid = df_copy['ath_time_diff_seconds'].notna().sum()
        atl_valid = df_copy['atl_time_diff_seconds'].notna().sum()
        total_rows = len(df_copy)

        print(f"\n时间差计算完成:")
        print(f"- 总行数: {total_rows}")
        print(f"- ATH时间差有效值: {ath_valid} ({ath_valid/total_rows*100:.1f}%)")
        print(f"- ATL时间差有效值: {atl_valid} ({atl_valid/total_rows*100:.1f}%)")

        # 显示时间差统计信息
        if ath_valid > 0:
            ath_stats = df_copy['ath_time_diff_seconds'].describe()
            print(f"\nATH时间差统计 (秒):")
            print(f"  最小值: {ath_stats['min']}")
            print(f"  最大值: {ath_stats['max']}")
            print(f"  平均值: {ath_stats['mean']:.1f}")
            print(f"  中位数: {ath_stats['50%']:.1f}")

        if atl_valid > 0:
            atl_stats = df_copy['atl_time_diff_seconds'].describe()
            print(f"\nATL时间差统计 (秒):")
            print(f"  最小值: {atl_stats['min']}")
            print(f"  最大值: {atl_stats['max']}")
            print(f"  平均值: {atl_stats['mean']:.1f}")
            print(f"  中位数: {atl_stats['50%']:.1f}")

        return df_copy

    def save_results(self, df, use_config_paths=True):
        """
        保存结果到文件

        Args:
            df (pd.DataFrame): 要保存的数据框
            use_config_paths (bool): 是否使用配置文件中的路径

        Returns:
            dict: 保存的文件路径
        """
        if use_config_paths:
            file_paths = config.get_dune_output_path(enhanced=True)
        else:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_paths = {
                'csv': f"dune_query_result_enhanced_{timestamp}.csv",
                'excel': f"dune_query_result_enhanced_{timestamp}.xlsx"
            }

        # 保存为CSV
        df.to_csv(file_paths['csv'], index=False, encoding=config.DATA_PROCESSING_CONFIG['encoding'])

        # 保存为Excel
        df.to_excel(file_paths['excel'], index=False)

        print(f"\n文件保存完成:")
        print(f"- CSV文件: {file_paths['csv']}")
        print(f"- Excel文件: {file_paths['excel']}")

        return file_paths

    def show_sample_data(self, df):
        """
        显示示例数据

        Args:
            df (pd.DataFrame): 数据框
        """
        if 'ath_time_diff_seconds' in df.columns and 'atl_time_diff_seconds' in df.columns:
            print("\n新增列示例数据:")

            # 显示时间相关列
            time_columns = ['ath_time', 'added_time', 'ath_time_diff_seconds',
                          'atl_time', 'atl_time_diff_seconds']
            available_columns = [col for col in time_columns if col in df.columns]

            if available_columns:
                print("时间差数据 (前5行):")
                print(df[available_columns].head().to_string())

            # 显示非空值的示例
            ath_valid_data = df[df['ath_time_diff_seconds'].notna()]
            if len(ath_valid_data) > 0:
                print(f"\nATH时间差非空值示例:")
                print(ath_valid_data[['ath_time', 'added_time', 'ath_time_diff_seconds']].head(3).to_string())

            atl_valid_data = df[df['atl_time_diff_seconds'].notna()]
            if len(atl_valid_data) > 0:
                print(f"\nATL时间差非空值示例:")
                print(atl_valid_data[['atl_time', 'added_time', 'atl_time_diff_seconds']].head(3).to_string())

    def run_complete_process(self):
        """
        运行完整的数据获取和处理流程

        Returns:
            dict: 处理结果和文件路径
        """
        try:
            # 1. 下载数据
            raw_data = self.download_data()

            # 2. 计算时间差
            enhanced_data = self.calculate_time_differences(raw_data)

            # 3. 显示示例数据
            self.show_sample_data(enhanced_data)

            # 4. 保存结果
            file_paths = self.save_results(enhanced_data)

            print(f"\n✅ 处理完成！")
            print(f"原始数据: {len(raw_data)} 行 × {len(raw_data.columns)} 列")
            print(f"增强数据: {len(enhanced_data)} 行 × {len(enhanced_data.columns)} 列")

            return {
                'success': True,
                'raw_data': raw_data,
                'enhanced_data': enhanced_data,
                'files': file_paths
            }

        except Exception as e:
            print(f"\n❌ 处理过程中出错: {e}")
            return {
                'success': False,
                'error': str(e)
            }

def main():
    """主函数"""
    print("🚀 增强版Dune数据下载器")
    print("=" * 50)

    # 显示配置信息
    config.print_config_summary()

    # 创建下载器实例（使用配置文件中的参数）
    downloader = EnhancedDuneDownloader()

    # 运行完整流程
    result = downloader.run_complete_process()

    if result['success']:
        print("\n🎉 所有操作成功完成！")
        print(f"📁 文件已保存到:")
        for file_type, file_path in result['files'].items():
            print(f"  - {file_type.upper()}: {file_path}")
    else:
        print(f"\n💥 操作失败: {result['error']}")
        sys.exit(1)

if __name__ == "__main__":
    main()
