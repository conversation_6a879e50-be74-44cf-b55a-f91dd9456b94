import webbrowser
import time

# 基础URL
base_url = "https://photon-sol.tinyastro.io/en/lp/"  # 替换成你的基础URL

def open_urls():
    print("请输入SOL地址（可以用空格、逗号或换行符分隔）：")
    print("输入完成后，按回车键两次确认完成输入")
    
    # 收集输入的地址
    addresses = []
    while True:
        line = input()
        if line == "":  # 空行表示输入结束
            break
        # 处理输入的行，分割地址
        line_addresses = line.replace("，", ",").replace("\n", " ").split(",")
        for addr in line_addresses:
            # 分割可能包含空格的地址
            addr_list = addr.split()
            addresses.extend(addr_list)

    # 过滤空地址并去重
    addresses = list(filter(None, addresses))
    addresses = list(dict.fromkeys(addresses))  # 去重

    print(f"\n找到 {len(addresses)} 个有效地址，开始打开网页...")

    # 打开每个地址对应的网页
    for i, address in enumerate(addresses, 1):
        full_url = base_url + address.strip() #+ "?remove_spam=true&exclude_amount_zero=true&flow=in&token_address=So11111111111111111111111111111111111111111&page=1#transfers"
        print(f"正在打开第 {i} 个网页: {full_url}")
        webbrowser.open_new_tab(full_url)
        time.sleep(1)  # 延迟1秒，避免浏览器负载过大

    print("\n所有网页已打开完成！")

if __name__ == "__main__":
    try:
        open_urls()
    except Exception as e:
        print(f"发生错误: {e}")
        
    
    input("\n按回车键退出程序...")
