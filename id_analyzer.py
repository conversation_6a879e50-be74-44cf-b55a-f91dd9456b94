#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel ID频次统计脚本
自动统计3个列（标签、推特caller、TGCaller）中的ID在aath=0和aath=1时的出现次数
生成一个包含所有详细统计的Excel文件
"""

import pandas as pd
import os
from collections import Counter
from datetime import datetime

def analyze_excel_ids(file_path):
    """分析Excel文件中的ID频次并生成完整统计报告"""
    
    # 读取Excel文件
    print(f"正在读取文件: {file_path}")
    df = pd.read_excel(file_path)
    print(f"数据加载完成: {df.shape[0]}行 × {df.shape[1]}列")
    
    # 检查必要的列
    if 'aath' not in df.columns:
        print("错误：文件中没有找到'aath'列")
        return
    
    # 要分析的ID列
    id_columns = ['标签', '推特caller', 'TGCaller']
    existing_columns = [col for col in id_columns if col in df.columns]
    
    if not existing_columns:
        print("错误：没有找到要分析的ID列")
        return
    
    print(f"将分析以下列: {existing_columns}")
    print("-" * 80)

    # 获取自定义文件名
    custom_name = input("请输入输出文件名（不含扩展名，默认为'ID频次统计报告'）: ").strip()
    if not custom_name:
        custom_name = "ID频次统计报告"

    # 添加时间戳（月-日_小时）
    timestamp = datetime.now().strftime("%m-%d_%H")
    output_file = f"{custom_name}_{timestamp}.xlsx"

    print(f"输出文件名: {output_file}")
    print("-" * 80)

    # 创建Excel写入器
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        
        # 汇总统计数据
        summary_data = []
        
        # 分析每个列
        for col in existing_columns:
            print(f"\n📊 分析列: {col}")
            
            # 统计ID在aath=0和aath=1时的出现次数
            aath_0_ids = Counter()
            aath_1_ids = Counter()
            
            for idx, row in df.iterrows():
                aath_value = row['aath']
                cell_value = row[col]
                
                # 解析ID（处理|分隔的多个ID）
                if pd.notna(cell_value) and str(cell_value).strip():
                    ids = [id_str.strip() for id_str in str(cell_value).split('|') if id_str.strip()]
                    
                    for id_str in ids:
                        if aath_value == 0:
                            aath_0_ids[id_str] += 1
                        elif aath_value == 1:
                            aath_1_ids[id_str] += 1
            
            # 合并所有ID并统计
            all_ids = set(aath_0_ids.keys()) | set(aath_1_ids.keys())
            
            # 创建详细结果列表
            results = []
            for id_str in all_ids:
                count_0 = aath_0_ids[id_str]
                count_1 = aath_1_ids[id_str]
                total = count_0 + count_1
                ratio = count_0 / count_1 if count_1 > 0 else float('inf') if count_0 > 0 else 0

                results.append({
                    'ID': id_str,
                    'aath=0次数': count_0,
                    'aath=1次数': count_1,
                    '总次数': total,
                    '0比1比值': ratio
                })
            
            # 按总次数排序
            results.sort(key=lambda x: x['总次数'], reverse=True)
            
            # 显示控制台统计摘要
            print(f"  唯一ID数量: {len(all_ids)}")
            print(f"  aath=0中总出现次数: {sum(aath_0_ids.values())}")
            print(f"  aath=1中总出现次数: {sum(aath_1_ids.values())}")
            print(f"  总出现次数: {sum(aath_0_ids.values()) + sum(aath_1_ids.values())}")
            
            # 显示前10个最频繁的ID
            print(f"  前10个最频繁ID:")
            for i, result in enumerate(results[:10], 1):
                ratio_str = f"{result['0比1比值']:.2f}" if result['0比1比值'] != float('inf') else "∞"
                print(f"    {i:2d}. {result['ID']:<25} | 总:{result['总次数']:3d} | 0:{result['aath=0次数']:3d} | 1:{result['aath=1次数']:3d} | 比值:{ratio_str}")

            # 保存详细结果到Excel工作表
            df_result = pd.DataFrame(results)
            df_result['0比1比值'] = df_result['0比1比值'].replace([float('inf')], '∞')
            df_result.to_excel(writer, sheet_name=f'{col}详细统计', index=False)
            
            # 添加到汇总数据
            summary_data.append({
                '列名': col,
                '唯一ID数量': len(all_ids),
                'aath=0总出现次数': sum(aath_0_ids.values()),
                'aath=1总出现次数': sum(aath_1_ids.values()),
                '总出现次数': sum(aath_0_ids.values()) + sum(aath_1_ids.values())
            })
        
        # 创建汇总统计表
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='汇总统计', index=False)
    
    print(f"\n✅ 完整统计报告已保存到: {output_file}")
    print(f"📊 包含 {len(existing_columns)} 个详细统计表和1个汇总表")

def main():
    """主函数"""
    print("=" * 80)
    print("📊 Excel ID频次统计工具")
    print("=" * 80)
    
    # 获取文件路径
    file_path = input("请输入Excel文件路径: ").strip().strip('"\'')
    
    if not os.path.exists(file_path):
        print(f"错误：文件不存在 - {file_path}")
        return
    
    if not file_path.lower().endswith(('.xlsx', '.xls')):
        print("错误：请提供Excel文件（.xlsx或.xls格式）")
        return
    
    try:
        analyze_excel_ids(file_path)
        print("\n" + "=" * 80)
        print("✅ 分析完成！")
        print("=" * 80)
    except Exception as e:
        print(f"错误：{str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
