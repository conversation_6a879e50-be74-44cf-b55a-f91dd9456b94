#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化版Excel数据处理和分拆工具
集成Telegram CPW消息搜索功能

功能类别	excel_processor_optimized.py	workflowauto(无需config).py
ATH列生成	✅ 完整(ath比值+athh+aath)	❌ 无
CPW搜索	✅ 完整CPW消息搜索	❌ 无
业务分组	✅ 4象限分组+独立文件	❌ 无
ID统计	✅ aath基础统计分析	❌ 无
字段编码	✅ 完整编码功能	✅ 相同功能

1. 读取Excel文件
2. 数据清洗和格式化
   ├── 空值处理
   ├── 特殊值清理
   ├── K值转换
   ├── 时间段列生成
   ├── ATH相关列生成 (ath比值 → athh → 删除athh=6 → aath)
   └── 类别特征编码
3. Telegram数据集成
   ├── CPW消息搜索
   └── 新增CPW相关列
4. ID统计分析
5. 业务分组
6. 多工作表文件创建
7. Excel格式化
8. 独立分组文件创建
9. 结果验证
"""

import pandas as pd
import numpy as np
import os
import argparse
from openpyxl import load_workbook
import sys
import asyncio
import re
import datetime
import pytz
from telethon import TelegramClient
from telethon.tl.types import InputMessagesFilterEmpty
from tqdm import tqdm

# 全局控制开关：是否对网站、Twitter、Telegram字段进行编码处理
ENABLE_FIELD_ENCODING = False

# Telegram API配置
API_ID = 24702520
API_HASH = '7b1f880deb5998f36a079cfdbd097534'
SESSION_NAME = 'telegram_cpw_finder_session'

# 要搜索的频道ID (请确保已加入这些频道)
CHANNEL_IDS = [
    -1001914959004,
    -1002050218130
]

# 默认CPW阈值
DEFAULT_CPW_THRESHOLD = 278  # 设置为0以显示所有找到的CPW值

# 默认搜索结果数量限制
DEFAULT_RESULT_LIMIT = 1

# ==================== Telegram 集成功能 ====================

async def extract_cpw_value(message_text):
    """
    从消息文本中提取CPW值

    Args:
        message_text (str): 消息文本

    Returns:
        float or None: 提取到的CPW值，如果未找到则返回None
    """
    # 处理Markdown链接格式: [**Avg CPW**](https://t.me/CallAnalyserPortal/130)**:** 530.13
    # 这种格式中，530.13是真正的CPW值
    markdown_link_patterns = [
        r'\[\*\*Avg\s*CPW\*\*\].*?\*\*:\*\*\s*(\d+\.?\d*)',  # 完整格式
        r'\*\*:\*\*\s*(\d+\.?\d*)',                          # 只匹配**:** 后面的数字
        r'Avg\s*CPW.*?:\s*(\d+\.?\d*)',                      # 匹配Avg CPW: 后面的数字
        r'\[\*\*Avg\s*CPW\*\*\].*?(\d+\.?\d*)',             # 简化的Markdown格式
        r'Avg\s*CPW.*?(\d+\.?\d*)',                          # 更宽松的Avg CPW匹配
    ]

    for pattern in markdown_link_patterns:
        match = re.search(pattern, message_text, re.IGNORECASE)
        if match:
            try:
                value = float(match.group(1))
                return value
            except ValueError:
                continue

    # 首先尝试提取Avg CPW: 数值格式
    avg_cpw_patterns = [
        r'Avg\s*CPW\s*:\s*(\d+\.?\d*)',  # Avg CPW: 453.5
        r'Avg\s*CPW\s*：\s*(\d+\.?\d*)',  # Avg CPW： 453.5 (中文冒号)
        r'Avg\s*CPW\s*=\s*(\d+\.?\d*)',  # Avg CPW = 453.5
        r'Avg\s*CPW\s*(\d+\.?\d*)',      # Avg CPW 453.5
        r'Avg\s*CPW[^\d]*(\d+\.?\d*)',   # Avg CPW后跟任意非数字字符，然后是数字
        r'[Aa]vg\s*[Cc][Pp][Ww][^\d]*(\d+\.?\d*)',  # 不区分大小写的Avg CPW
        r'Average\s*CPW\s*:\s*(\d+\.?\d*)',  # Average CPW: 453.5
        r'Average\s*CPW[^\d]*(\d+\.?\d*)',   # Average CPW后跟任意非数字字符，然后是数字
        r'平均\s*CPW\s*:\s*(\d+\.?\d*)',     # 中文：平均CPW: 453.5
    ]

    for pattern in avg_cpw_patterns:
        match = re.search(pattern, message_text, re.IGNORECASE)
        if match:
            try:
                value = float(match.group(1))
                return value
            except ValueError:
                continue

    # 如果没有找到Avg CPW，尝试提取普通CPW值
    cpw_patterns = [
        r'CPW\s*:\s*(\d+\.?\d*)',  # CPW: 302.73
        r'CPW\s*：\s*(\d+\.?\d*)',  # CPW： 302.73 (中文冒号)
        r'CPW\s*=\s*(\d+\.?\d*)',  # CPW = 302.73
        r'CPW\s*(\d+\.?\d*)',       # CPW 302.73
        r'CPW[^\d]*(\d+\.?\d*)',    # CPW后跟任意非数字字符，然后是数字
        r'[Cc][Pp][Ww][^\d]*(\d+\.?\d*)',  # 不区分大小写的CPW
        r'Cost\s*Per\s*Wallet\s*:\s*(\d+\.?\d*)',  # Cost Per Wallet: 302.73
        r'Cost\s*Per\s*Wallet[^\d]*(\d+\.?\d*)',   # Cost Per Wallet后跟数字
        r'每钱包成本\s*:\s*(\d+\.?\d*)',            # 中文：每钱包成本: 302.73
        r'钱包成本\s*:\s*(\d+\.?\d*)',              # 中文：钱包成本: 302.73
    ]

    for pattern in cpw_patterns:
        match = re.search(pattern, message_text, re.IGNORECASE)
        if match:
            try:
                value = float(match.group(1))
                return value
            except ValueError:
                continue

    return None

async def parse_date_string(date_str):
    """
    解析多种格式的日期字符串，确保返回UTC时区的datetime对象

    Args:
        date_str (str): 日期字符串

    Returns:
        datetime: 解析后的日期时间对象（UTC时区）
    """
    import pytz

    # 尝试多种日期格式
    formats = [
        "%Y-%m-%d %H:%M:%S.%f %Z",  # 2025-05-12 01:28:16.000 UTC
        "%Y-%m-%d %H:%M:%S.%f",     # 2025-05-12 01:28:16.000
        "%Y-%m-%d %H:%M:%S %Z",     # 2025-05-12 01:28:16 UTC
        "%Y-%m-%d %H:%M:%S",        # 2025-05-12 01:28:16
        "%Y-%m-%d %H:%M:%S+00:00",  # 2025-05-05 14:43:54+00:00 (API返回格式)
        "%Y-%m-%d %H:%M:%S%z",      # 2025-05-05 14:43:54+0000 (带时区偏移)
        "%Y-%m-%d %H:%M %Z",        # 2025-05-12 01:28 UTC
        "%Y-%m-%d %H:%M",           # 2025-05-12 01:28
        "%Y-%m-%d"                  # 2025-05-12
    ]

    # 首先尝试直接使用datetime的fromisoformat方法（处理ISO 8601格式，如2025-05-05T14:43:54+00:00）
    try:
        # 将T分隔符替换为空格，以便处理2025-05-05T14:43:54+00:00格式
        iso_date_str = date_str.replace(' ', 'T') if 'T' not in date_str else date_str
        parsed_date = datetime.datetime.fromisoformat(iso_date_str)
        # 如果没有时区信息，假设为UTC
        if parsed_date.tzinfo is None:
            parsed_date = pytz.UTC.localize(parsed_date)
        return parsed_date
    except ValueError:
        pass

    # 然后尝试各种格式
    for fmt in formats:
        try:
            parsed_date = datetime.datetime.strptime(date_str, fmt)
            # 如果解析成功但没有时区信息，假设为UTC
            if parsed_date.tzinfo is None:
                # 特殊处理：如果原字符串包含UTC，则设为UTC时区
                if 'UTC' in date_str.upper():
                    parsed_date = pytz.UTC.localize(parsed_date)
                else:
                    # 否则也假设为UTC（因为大多数情况下输入都是UTC时间）
                    parsed_date = pytz.UTC.localize(parsed_date)
            return parsed_date
        except ValueError:
            continue

    # 如果所有格式都不匹配，抛出异常
    raise ValueError(f"无法解析日期字符串: {date_str}")

async def search_messages(client, channel_id, token_address, after_date, cpw_threshold, limit, verbose=False):
    """
    在指定频道中搜索包含特定token_address且CPW值大于阈值的消息

    Args:
        client (TelegramClient): Telegram客户端
        channel_id (int): 频道ID
        token_address (str): token_address
        after_date (datetime): 搜索此日期之后的消息
        cpw_threshold (float): CPW值阈值
        limit (int): 返回结果数量限制
        verbose (bool): 是否显示详细信息，包括消息内容

    Returns:
        list: 符合条件的消息列表，每个元素为(cpw_value, message_date, message_id, time_diff)元组，
             其中time_diff是消息时间与added_time的差值（秒）
    """
    try:
        print(f"\n🔍 搜索频道 {channel_id} 中包含地址 {token_address} 的消息...")
        print(f"   搜索条件: 日期 > {after_date}, CPW值 > {cpw_threshold}")

        # 获取频道实体
        channel = await client.get_entity(channel_id)
        print(f"   ✅ 成功获取频道信息: {getattr(channel, 'title', str(channel_id))}")

        # 搜索包含token_address的消息
        messages = []
        message_count = 0
        found_message_count = 0

        # 使用完整地址搜索
        print(f"   🔎 使用完整地址搜索: {token_address}")
        async for message in client.iter_messages(
            channel,
            search=token_address,
            filter=InputMessagesFilterEmpty(),
            limit=100  # 增加搜索结果数量，以获取更多消息
        ):
            message_count += 1
            # 检查消息日期是否在指定日期之后
            msg_date_utc = message.date
            if after_date.tzinfo is None:
                import pytz
                after_date_utc = pytz.UTC.localize(after_date)
            else:
                after_date_utc = after_date

            if msg_date_utc > after_date_utc:
                # 更严格的排除关键词检查 - 只排除明确的汇总消息
                if message.text and (
                    ("Total Call" in message.text and "Summary" in message.text) or
                    ("Main Calls" in message.text and "Summary" in message.text) or
                    ("Daily Summary" in message.text) or
                    ("Weekly Summary" in message.text)
                ):
                    print(f"      ⏭️ 跳过汇总消息: ID={message.id}")
                    continue

                found_message_count += 1
                print(f"      📝 找到消息 ID: {message.id}, 日期: {message.date}")
                if message.text:
                    # 提取CPW值
                    cpw_value = await extract_cpw_value(message.text)
                    # 计算时间差（秒）- 确保时区一致性
                    msg_date = message.date  # 保持原始时区信息
                    # 如果after_date没有时区信息，转换为UTC
                    if after_date.tzinfo is None:
                        import pytz
                        after_date_utc = pytz.UTC.localize(after_date)
                    else:
                        after_date_utc = after_date
                    time_diff = (msg_date - after_date_utc).total_seconds()

                    if cpw_value is not None:
                        if cpw_value > cpw_threshold:
                            print(f"         ✅ 消息符合条件: CPW值 = {cpw_value} > {cpw_threshold}")
                            messages.append((cpw_value, message.date, message.id, time_diff))
                        else:
                            print(f"         ❌ 消息CPW值 = {cpw_value} <= {cpw_threshold}，不符合条件")
                    else:
                        # 如果没有找到CPW值但阈值为0，也保存消息
                        if cpw_threshold <= 0:
                            print(f"         ℹ️ 未找到CPW值，但由于阈值为{cpw_threshold}，仍然保存消息")
                            messages.append((0, message.date, message.id, time_diff))

        print(f"   📊 搜索结果: 共找到 {message_count} 条消息，其中 {found_message_count} 条在指定日期之后，{len(messages)} 条符合CPW值条件")

        # 按日期排序，返回距离指定时间点最近的几条消息
        # 按照时间排序（按消息发布时间排序）
        sorted_msgs = sorted(messages, key=lambda x: x[1])

        return sorted_msgs[:limit]

    except Exception as e:
        print(f"❌ 搜索频道 {channel_id} 时出错: {e}")
        import traceback
        traceback.print_exc()
        return []

async def process_telegram_data(df, cpw_threshold=DEFAULT_CPW_THRESHOLD, limit=DEFAULT_RESULT_LIMIT):
    """
    处理DataFrame中的Telegram数据搜索

    Args:
        df (DataFrame): 包含token_address和added_time的数据框
        cpw_threshold (float): CPW值阈值
        limit (int): 每个代币返回的结果数量限制

    Returns:
        DataFrame: 添加了CPW相关列的数据框
    """
    print("📡 开始Telegram CPW数据处理...")

    # 检查必要的列是否存在
    required_columns = ['token_address', 'added_time']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"   ⚠️ 缺少必要的列: {', '.join(missing_columns)}，跳过Telegram数据处理")
        return df

    # 创建副本
    df_processed = df.copy()

    # 创建客户端
    client = TelegramClient(SESSION_NAME, API_ID, API_HASH)

    try:
        await client.start()
        print("   ✅ Telegram客户端连接成功")

        # 为结果创建新列
        for i in range(1, limit + 1):
            if f'CPW_{i}' not in df_processed.columns:
                df_processed[f'CPW_{i}'] = None
            if f'消息时间_{i}' not in df_processed.columns:
                df_processed[f'消息时间_{i}'] = None
            if f'时间差_{i}' not in df_processed.columns:
                df_processed[f'时间差_{i}'] = None

        # 处理每一行
        processed_count = 0
        skipped_count = 0

        for index, row in tqdm(df_processed.iterrows(), total=len(df_processed), desc="处理代币Telegram数据"):
            token_address = row['token_address']
            after_date_str = row['added_time']

            # 跳过空值
            if pd.isna(token_address) or pd.isna(after_date_str):
                skipped_count += 1
                continue

            # 解析日期字符串
            try:
                after_date = await parse_date_string(str(after_date_str))
            except ValueError as e:
                print(f"   ⚠️ 行 {index+1}: {str(e)}")
                skipped_count += 1
                continue

            # 搜索所有频道
            all_results = []
            for channel_id in CHANNEL_IDS:
                results = await search_messages(client, channel_id, token_address, after_date, cpw_threshold, 100)
                all_results.extend(results)

            # 按日期排序，获取距离指定时间点最近的几条消息
            # 按照时间排序，获取最早的几条消息
            sorted_results = sorted(all_results, key=lambda x: x[1])[:limit]

            # 更新DataFrame
            for i, result in enumerate(sorted_results, 1):
                if i <= limit:
                    cpw_value, message_date, _, time_diff = result
                    df_processed.at[index, f'CPW_{i}'] = cpw_value
                    df_processed.at[index, f'消息时间_{i}'] = message_date.strftime("%Y-%m-%d %H:%M:%S")
                    df_processed.at[index, f'时间差_{i}'] = time_diff

            processed_count += 1

            # 每处理完一个代币后等待，避免API限制
            if index < len(df_processed) - 1:  # 如果不是最后一个代币
                await asyncio.sleep(0.3)

        print(f"   ✅ Telegram数据处理完成: 处理了 {processed_count} 个代币，跳过了 {skipped_count} 个无效行")

    except Exception as e:
        print(f"   ❌ Telegram数据处理失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await client.disconnect()
        print("   🔌 Telegram客户端已断开连接")

    return df_processed

# ==================== 原有功能 ====================



def calculate_time_period(time_value):
    """根据时间值计算时间段(0-7)"""
    if pd.isna(time_value) or time_value == -1:
        return -1

    try:
        # 如果是字符串，尝试解析时间
        if isinstance(time_value, str):
            # 处理类似"2025-05-07 05:16:08.000 UTC"的时间格式
            import datetime
            # 去掉UTC后缀
            time_str = time_value.replace(' UTC', '').strip()
            # 尝试解析时间
            if '.' in time_str:
                dt = datetime.datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S.%f')
            else:
                dt = datetime.datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
            hour = dt.hour
        elif isinstance(time_value, (int, float)):
            hour = int(time_value)
        else:
            # 如果是datetime对象
            hour = time_value.hour

        # 0-2点对应0，3-5点对应1，以此类推
        return hour // 3
    except (ValueError, TypeError, AttributeError):
        return -1

def calculate_ath_category(ath_value):
    """根据ath值计算athh分类(0-2)"""
    if pd.isna(ath_value):
        return 0

    try:
        ath = float(ath_value)
        if ath <= 165:
            return 0
        elif 166 <= ath <= 699:
            return 2
        else:  # 700以上
            return 1
    except (ValueError, TypeError):
        return 0

def calculate_ath_ratio(ath_value, atl_bf_ath_value):
    """计算ath/atl_bf_ath的比值"""
    if pd.isna(ath_value) or pd.isna(atl_bf_ath_value):
        return None

    try:
        ath = float(ath_value)
        atl_bf_ath = float(atl_bf_ath_value)
        if atl_bf_ath == 0:
            return None
        return ath / atl_bf_ath
    except (ValueError, TypeError, ZeroDivisionError):
        return None

def calculate_aath_value(athh_value, ath_ratio_value):
    """计算aath值：当athh=1且ath比值≥8时，aath=1；否则aath=0"""
    try:
        # 检查athh值是否为1
        if pd.isna(athh_value) or int(athh_value) != 1:
            return 0

        # 检查ath比值是否≥8
        if pd.isna(ath_ratio_value):
            return 0

        ath_ratio = float(ath_ratio_value)
        if ath_ratio >= 8:
            return 1
        else:
            return 0
    except (ValueError, TypeError):
        return 0

def calculate_column_median(series):
    """计算列的中位数，忽略空值和非数值"""
    try:
        # 过滤掉空值和非数值
        numeric_values = pd.to_numeric(series, errors='coerce').dropna()
        if len(numeric_values) > 0:
            return numeric_values.median()
        else:
            return 0  # 如果没有有效数值，返回0
    except Exception:
        return 0

def process_missing_values_by_rules(df, field_rules):
    """根据规则处理空值/缺失值

    Args:
        df: DataFrame
        field_rules: 字段规则列表，格式如 ['字段名-数字', '字段名-mid']
    """
    print("🔧 开始处理空值/缺失值...")

    df_processed = df.copy()

    for rule in field_rules:
        if '-' not in rule:
            continue

        # 解析字段名和规则
        parts = rule.rsplit('-', 1)  # 从右边分割，处理字段名中可能包含'-'的情况
        if len(parts) != 2:
            continue

        field_name = parts[0].strip()
        rule_value = parts[1].strip()

        # 检查字段是否存在
        if field_name not in df_processed.columns:
            print(f"   ⚠️ 字段不存在，跳过: {field_name}")
            continue

        # 特殊处理24H换手率列：处理空值和非数字值
        if field_name == '24H换手率' and rule_value == 'mid':
            # 识别空值和非数字值
            numeric_mask = pd.to_numeric(df_processed[field_name], errors='coerce').notna()
            non_numeric_count = (~numeric_mask).sum()

            if non_numeric_count > 0:
                # 计算有效数字值的中位数
                median_value = calculate_column_median(df_processed[field_name])
                # 将空值和非数字值都替换为中位数
                df_processed.loc[~numeric_mask, field_name] = median_value
                print(f"   ✅ {field_name}: {non_numeric_count} 个空值和非数字值替换为中位数 {median_value}")
            else:
                print(f"   ℹ️ {field_name}: 无空值或非数字值，跳过处理")
            continue

        # 统计空值数量
        missing_count = df_processed[field_name].isna().sum()
        if missing_count == 0:
            print(f"   ℹ️ {field_name}: 无空值，跳过处理")
            continue

        # 根据规则处理
        if rule_value == 'mid':
            # 使用中位数填充
            median_value = calculate_column_median(df_processed[field_name])
            df_processed[field_name] = df_processed[field_name].fillna(median_value)
            print(f"   ✅ {field_name}: {missing_count} 个空值替换为中位数 {median_value}")
        else:
            # 尝试转换为数字
            try:
                fill_value = float(rule_value)
                df_processed[field_name] = df_processed[field_name].fillna(fill_value)
                print(f"   ✅ {field_name}: {missing_count} 个空值替换为 {fill_value}")
            except ValueError:
                print(f"   ⚠️ {field_name}: 无效的填充值 '{rule_value}'，跳过处理")

    return df_processed

def encode_website_column(value):
    """编码网站列的值
    0=空值，1=包含app，2=包含pump.fun/bit.ly/discord/t.co/t.me，
    3=包含truthsocial/twitter/tiktok/instagram/reddit/x.com/youtu，4=其他
    """
    if pd.isna(value) or value == '' or str(value).strip() == '':
        return 0

    value_str = str(value).lower().strip()

    # 检查是否包含app
    if 'app' in value_str:
        return 1

    # 检查第2类关键词
    type2_keywords = ['https://pump.fun', 'https://bit.ly', 'discord', 'https://t.co', 'https://t.me']
    for keyword in type2_keywords:
        if keyword.lower() in value_str:
            return 2

    # 检查第3类关键词
    type3_keywords = ['https://truthsocial', 'https://twitter', 'tiktok', 'instagram', 'reddit', 'https://x.com', 'youtu']
    for keyword in type3_keywords:
        if keyword.lower() in value_str:
            return 3

    # 其他所有内容
    return 4

def encode_twitter_column(value):
    """编码Twitter列的值
    0=空值，1=twitter.com/x.com+/status，2=twitter.com/x.com+i/communities，
    3=twitter.com/x.com但排除前两种，4=其他
    """
    if pd.isna(value) or value == '' or str(value).strip() == '':
        return 0

    value_str = str(value).lower().strip()

    # 检查是否包含twitter.com或x.com
    has_twitter = 'twitter.com' in value_str or 'x.com' in value_str

    if has_twitter:
        # 检查是否包含/status模式
        if '/status' in value_str:
            return 1
        # 检查是否包含i/communities模式
        elif 'i/communities' in value_str:
            return 2
        # 其他twitter.com/x.com内容
        else:
            return 3

    # 不包含twitter.com或x.com的其他内容
    return 4

def encode_telegram_column(value):
    """编码Telegram列的值
    0=空值，1=包含https://t.me，2=其他
    """
    if pd.isna(value) or value == '' or str(value).strip() == '':
        return 0

    value_str = str(value).lower().strip()

    # 检查是否包含https://t.me
    if 'https://t.me' in value_str:
        return 1

    # 其他所有内容
    return 2

def process_categorical_encoding(df):
    """处理类别特征编码转换"""
    print("🔧 开始类别特征编码转换...")

    df_processed = df.copy()

    # 检查全局控制开关
    if not ENABLE_FIELD_ENCODING:
        print("   ℹ️ 字段编码功能已禁用，跳过编码处理")
        return df_processed

    # 处理网站列 - 保留原始数据，新增编码列
    if '网站' in df_processed.columns:
        # 在原始列旁边新增编码列
        website_col_idx = df_processed.columns.get_loc('网站')
        encoded_values_series = df_processed['网站'].apply(encode_website_column)
        df_processed.insert(website_col_idx + 1, '网站_编码', encoded_values_series)

        encoded_values = encoded_values_series.value_counts().sort_index()
        print(f"   ✅ 网站: 保留原始数据，新增编码列")
        print(f"      编码分布: {dict(encoded_values)}")
    else:
        print(f"   ℹ️ 网站: 列不存在，跳过处理")

    # 处理Twitter列 - 保留原始数据，新增编码列
    if 'Twitter' in df_processed.columns:
        # 在原始列旁边新增编码列
        twitter_col_idx = df_processed.columns.get_loc('Twitter')
        encoded_values_series = df_processed['Twitter'].apply(encode_twitter_column)
        df_processed.insert(twitter_col_idx + 1, 'Twitter_编码', encoded_values_series)

        encoded_values = encoded_values_series.value_counts().sort_index()
        print(f"   ✅ Twitter: 保留原始数据，新增编码列")
        print(f"      编码分布: {dict(encoded_values)}")
    else:
        print(f"   ℹ️ Twitter: 列不存在，跳过处理")

    # 处理Telegram列 - 保留原始数据，新增编码列
    if 'Telegram' in df_processed.columns:
        # 在原始列旁边新增编码列
        telegram_col_idx = df_processed.columns.get_loc('Telegram')
        encoded_values_series = df_processed['Telegram'].apply(encode_telegram_column)
        df_processed.insert(telegram_col_idx + 1, 'Telegram_编码', encoded_values_series)

        encoded_values = encoded_values_series.value_counts().sort_index()
        print(f"   ✅ Telegram: 保留原始数据，新增编码列")
        print(f"      编码分布: {dict(encoded_values)}")
    else:
        print(f"   ℹ️ Telegram: 列不存在，跳过处理")

    return df_processed

def clean_data_optimized(df):
    """优化的数据清洗函数"""
    print("🔧 开始数据清洗和格式化...")

    # 创建副本
    df_cleaned = df.copy()

    # 1. 处理特定列的缺失值 -> 替换为 -1
    missing_to_negative_one = ['山丘之王时间戳', '进程1阶段', '创建时间', '开发者持有占比']
    for col in missing_to_negative_one:
        if col in df_cleaned.columns:
            missing_count = df_cleaned[col].isna().sum()
            df_cleaned[col] = df_cleaned[col].fillna(-1)
            print(f"   ✅ {col}: {missing_count} 个缺失值替换为 -1")

    # 2. 处理'描述'列的缺失值 -> 替换为 2
    if '描述' in df_cleaned.columns:
        missing_count = df_cleaned['描述'].isna().sum()
        df_cleaned['描述'] = df_cleaned['描述'].fillna(2)
        print(f"   ✅ 描述: {missing_count} 个缺失值替换为 2")

    # 3. 新增功能：处理'DexPaid支付时间'列空值替换为-1
    if 'DexPaid支付时间' in df_cleaned.columns:
        missing_count = df_cleaned['DexPaid支付时间'].isna().sum()
        df_cleaned['DexPaid支付时间'] = df_cleaned['DexPaid支付时间'].fillna(-1)
        print(f"   ✅ DexPaid支付时间: {missing_count} 个缺失值替换为 -1")



    # 6. 新增功能：在'added_time'列右边新增'时间段'列
    if 'added_time' in df_cleaned.columns and '时间段' not in df_cleaned.columns:
        # 找到added_time列的位置
        added_time_idx = df_cleaned.columns.get_loc('added_time')
        # 计算时间段
        time_periods = df_cleaned['added_time'].apply(calculate_time_period)
        # 在added_time列右边插入新列
        df_cleaned.insert(added_time_idx + 1, '时间段', time_periods)
        print(f"   ✅ 时间段: 在added_time列右边新增时间段列")
    elif '时间段' in df_cleaned.columns:
        # 如果列已存在，重新计算值
        df_cleaned['时间段'] = df_cleaned['added_time'].apply(calculate_time_period)
        print(f"   ✅ 时间段: 重新计算时间段列")

    # 6.1 新增功能：计算added_time和launch_time的时间差
    if 'added_time' in df_cleaned.columns and 'launch_time' in df_cleaned.columns and '时间差' not in df_cleaned.columns:
        # 找到launch_time列的位置
        launch_time_idx = df_cleaned.columns.get_loc('launch_time')

        # 计算时间差（秒）
        def calculate_time_diff(row):
            try:
                if pd.isna(row['added_time']) or pd.isna(row['launch_time']):
                    return None

                # 使用pandas解析时间
                added_time = pd.to_datetime(str(row['added_time']), utc=True)
                launch_time = pd.to_datetime(str(row['launch_time']), utc=True)

                # 计算时间差（秒）
                time_diff = (added_time - launch_time).total_seconds()
                return time_diff
            except Exception:
                return None

        # 计算时间差
        time_diffs = df_cleaned.apply(calculate_time_diff, axis=1)
        # 在launch_time列右边插入新列
        df_cleaned.insert(launch_time_idx + 1, '时间差', time_diffs)
        print(f"   ✅ 时间差: 在launch_time列右边新增时间差列（单位：秒）")
    elif '时间差' in df_cleaned.columns:
        # 如果列已存在，重新计算值
        def calculate_time_diff(row):
            try:
                if pd.isna(row['added_time']) or pd.isna(row['launch_time']):
                    return None

                # 使用pandas解析时间
                added_time = pd.to_datetime(str(row['added_time']), utc=True)
                launch_time = pd.to_datetime(str(row['launch_time']), utc=True)

                # 计算时间差（秒）
                time_diff = (added_time - launch_time).total_seconds()
                return time_diff
            except Exception:
                return None

        df_cleaned['时间差'] = df_cleaned.apply(calculate_time_diff, axis=1)
        print(f"   ✅ 时间差: 重新计算时间差列（单位：秒）")

    # 7. 新增功能：在'ath'列左边新增'ath比值'列
    if 'ath' in df_cleaned.columns and 'atl_bf_ath' in df_cleaned.columns and 'ath比值' not in df_cleaned.columns:
        # 找到ath列的位置
        ath_idx = df_cleaned.columns.get_loc('ath')
        # 计算ath比值
        ath_ratios = df_cleaned.apply(lambda row: calculate_ath_ratio(row['ath'], row['atl_bf_ath']), axis=1)
        # 在ath列左边插入新列
        df_cleaned.insert(ath_idx, 'ath比值', ath_ratios)
        print(f"   ✅ ath比值: 在ath列左边新增ath比值列")
    elif 'ath比值' in df_cleaned.columns:
        # 如果列已存在，重新计算值
        df_cleaned['ath比值'] = df_cleaned.apply(lambda row: calculate_ath_ratio(row['ath'], row['atl_bf_ath']), axis=1)
        print(f"   ✅ ath比值: 重新计算ath比值列")

    # 8. 新增功能：在'ath'列右边新增'athh'列
    if 'ath' in df_cleaned.columns and 'athh' not in df_cleaned.columns:
        # 找到ath列的位置（注意可能因为前面插入了ath比值列而位置发生变化）
        ath_idx = df_cleaned.columns.get_loc('ath')
        # 计算athh分类
        ath_categories = df_cleaned['ath'].apply(calculate_ath_category)
        # 在ath列右边插入新列
        df_cleaned.insert(ath_idx + 1, 'athh', ath_categories)
        print(f"   ✅ athh: 在ath列右边新增athh分类列")
    elif 'athh' in df_cleaned.columns:
        # 如果列已存在，重新计算值
        df_cleaned['athh'] = df_cleaned['ath'].apply(calculate_ath_category)
        print(f"   ✅ athh: 重新计算athh分类列")

    # 8.1 新增功能：删除athh=6的行
    if 'athh' in df_cleaned.columns:
        # 统计athh=6的行数
        athh_6_mask = df_cleaned['athh'] == 6
        athh_6_count = athh_6_mask.sum()

        if athh_6_count > 0:
            # 删除athh=6的行
            df_cleaned = df_cleaned[~athh_6_mask].reset_index(drop=True)
            print(f"   ✅ ATH数据清理: 删除了 {athh_6_count} 行athh=6的数据")
        else:
            print(f"   ℹ️ ATH数据清理: 未发现athh=6的数据")

    # 8.2 新增功能：在'athh'列右边新增'aath'列
    if 'athh' in df_cleaned.columns and 'ath比值' in df_cleaned.columns and 'aath' not in df_cleaned.columns:
        # 找到athh列的位置
        athh_idx = df_cleaned.columns.get_loc('athh')
        # 计算aath值
        aath_values = df_cleaned.apply(lambda row: calculate_aath_value(row['athh'], row['ath比值']), axis=1)
        # 在athh列右边插入新列
        df_cleaned.insert(athh_idx + 1, 'aath', aath_values)

        # 统计aath值分布
        aath_distribution = aath_values.value_counts().sort_index()
        print(f"   ✅ aath: 在athh列右边新增aath列")
        print(f"      aath分布: {dict(aath_distribution)}")
    elif 'aath' in df_cleaned.columns:
        # 如果列已存在，重新计算值
        if 'ath比值' in df_cleaned.columns:
            df_cleaned['aath'] = df_cleaned.apply(lambda row: calculate_aath_value(row['athh'], row['ath比值']), axis=1)
            aath_distribution = df_cleaned['aath'].value_counts().sort_index()
            print(f"   ✅ aath: 重新计算aath列")
            print(f"      aath分布: {dict(aath_distribution)}")
        else:
            print(f"   ⚠️ aath: 缺少ath比值列，无法计算aath值")



    # 4. 确保关键数值列为数值类型
    numeric_columns = ['山丘之王时间戳', '进程1阶段', '创建时间', '开发者持有占比', '描述']
    for col in numeric_columns:
        if col in df_cleaned.columns:
            try:
                df_cleaned[col] = pd.to_numeric(df_cleaned[col], errors='coerce')
                # 处理转换后的NaN
                if col in missing_to_negative_one:
                    df_cleaned[col] = df_cleaned[col].fillna(-1)
                elif col == '描述':
                    df_cleaned[col] = df_cleaned[col].fillna(2)
                print(f"   ✅ {col}: 转换为数值类型")
            except Exception as e:
                print(f"   ⚠️ {col}: 数值转换失败 - {e}")

    # 10. 新增功能：根据规则处理空值/缺失值
    field_rules = [
        '狙击数-mid', '狙击占比-mid', '内幕数-mid', '内幕占比-mid', 'top 10占比-mid', 'holder人数-mid',
        '1m Change-mid', '5m Change-mid', '开发者代币总数-mid', '已迁移代币数-mid', '持有者数量-mid',
        '机器人用户数-mid', '前10大持有者占比-mid', '开发者持有占比-mid', '内部人士持有占比-mid',
        '打包者持有占比-mid', '狙击手持有占比-mid', '总配对费用-mid', 'Cabal-mid', 'Phishing-mid',
        'Insiders-mid', 'Bundle-mid', 'Snipers-mid', 'DEV-mid', 'Whale-mid', 'Smarters-mid',
        'KOL-mid', 'Insiders_RUG-mid', 'Phishing_RUG-mid', 'Cabal_RUG-mid', 'Bundle_RUG-mid',
        'All Tag Rate-mid', '蓝筹持有者占比-mid', '信号数量-mid', 'Degen呼叫数-mid',
        '顶级老鼠交易者占比-mid', '顶级打包者占比-mid', '顶级陷阱交易者占比-mid', '平均持有余额-mid',
        '前10持有者占比-mid', '前100持有者占比-mid', '智能钱包-mid', '新钱包-mid', '知名钱包-mid',
        '创建者钱包-mid', '狙击手钱包-mid', '老鼠交易者钱包-mid', '鲸鱼钱包-mid', '顶级钱包-mid',
        '关注钱包-mid', '打包者钱包-mid', 'Twitter变更次数-mid', 'Twitter删除帖子代币数-mid',
        'Twitter创建代币数-mid', '创建者代币余额-mid', 'DEX广告状态-mid', 'CTO标志-mid',
        '当前价格-mid', '1分钟价格-mid', '1分钟买入次数-mid', '1分钟卖出次数-mid', '1分钟交易量-mid',
        '1分钟买入量-mid', '1分钟卖出量-mid', '5分钟买入次数-mid', '5分钟卖出次数-mid',
        '5分钟交易量-mid', '5分钟买入量-mid', '5分钟卖出量-mid', '热度等级-mid', 'hold-mid',
        'bought_more-mid', 'transfered-mid', 'bought_rate-mid', 'holding_rate-mid',
        '符合条件的钱包数量-mid', '最大持有者占比-mid', 'Axiom持有者数量-mid', 'GMGN持有者数量-mid',
        '6的个数-mid', 'photon-mid', 'null统计-mid', 'transfer_in次数-mid', 'is_new次数-mid',
        'is_suspicious次数-mid', 'sandwich_bot次数-mid', 'bundler次数-mid'
    ]
    df_cleaned = process_missing_values_by_rules(df_cleaned, field_rules)

    # 11. 新增功能：类别特征编码转换
    df_cleaned = process_categorical_encoding(df_cleaned)

    print(f"✅ 数据清洗完成")
    return df_cleaned

def analyze_id_statistics(df):
    """分析ID统计：统计3个列（标签、推特caller名称、TGCaller名称）中的ID在aath=0和aath=1时的出现次数"""
    print("📊 开始ID统计分析...")

    # 检查必要的列
    if 'aath' not in df.columns:
        print("   ⚠️ 缺少'aath'列，跳过ID统计分析")
        return None

    # 要分析的ID列
    id_columns = ['标签', '推特caller名称', 'TGCaller名称']
    existing_columns = [col for col in id_columns if col in df.columns]

    if not existing_columns:
        print("   ⚠️ 缺少必要的ID列（标签、推特caller名称、TGCaller名称），跳过ID统计分析")
        return None

    print(f"   📋 分析列: {existing_columns}")

    # 存储所有统计结果
    analysis_results = {}
    summary_data = []

    # 分析每个列
    for col in existing_columns:
        print(f"   🔍 分析列: {col}")

        # 统计ID在aath=0和aath=1时的出现次数
        from collections import Counter
        aath_0_ids = Counter()
        aath_1_ids = Counter()

        for idx, row in df.iterrows():
            aath_value = row['aath']
            cell_value = row[col]

            # 解析ID（处理|分隔的多个ID）
            if pd.notna(cell_value) and str(cell_value).strip():
                ids = [id_str.strip() for id_str in str(cell_value).split('|') if id_str.strip()]

                for id_str in ids:
                    if aath_value == 0:
                        aath_0_ids[id_str] += 1
                    elif aath_value == 1:
                        aath_1_ids[id_str] += 1

        # 合并所有ID并统计
        all_ids = set(aath_0_ids.keys()) | set(aath_1_ids.keys())

        # 创建详细结果列表
        results = []
        for id_str in all_ids:
            count_0 = aath_0_ids[id_str]
            count_1 = aath_1_ids[id_str]
            total = count_0 + count_1
            ratio = count_0 / count_1 if count_1 > 0 else float('inf') if count_0 > 0 else 0

            results.append({
                'ID': id_str,
                'aath=0次数': count_0,
                'aath=1次数': count_1,
                '总次数': total,
                '0比1比值': ratio
            })

        # 按总次数排序
        results.sort(key=lambda x: x['总次数'], reverse=True)

        # 显示统计摘要
        total_0_count = sum(aath_0_ids.values())
        total_1_count = sum(aath_1_ids.values())
        print(f"      唯一ID数量: {len(all_ids)}")
        print(f"      aath=0总出现次数: {total_0_count}")
        print(f"      aath=1总出现次数: {total_1_count}")
        print(f"      总出现次数: {total_0_count + total_1_count}")

        # 存储结果
        analysis_results[col] = results

        # 添加到汇总数据
        summary_data.append({
            '列名': col,
            '唯一ID数量': len(all_ids),
            'aath=0总出现次数': total_0_count,
            'aath=1总出现次数': total_1_count,
            '总出现次数': total_0_count + total_1_count
        })

    print(f"   ✅ ID统计分析完成，共分析 {len(existing_columns)} 个列")

    # 返回分析结果
    return {
        'detailed_results': analysis_results,
        'summary_data': summary_data
    }

def create_business_groups(df):
    """创建业务分组"""
    print("📊 创建业务分组...")

    # 检查必要列
    if '创建时间' not in df.columns or '开发者持有占比' not in df.columns:
        raise ValueError("缺少必要的分拆列: 创建时间, 开发者持有占比")

    # 定义分拆条件
    cond1 = df['创建时间'] > 5
    cond2 = df['开发者持有占比'] < 0.2
    cond3 = df['开发者持有占比'] >= 0.2

    # 创建4个分组
    groups = {
        '高创建时间_低Dev持有': cond1 & cond2,      # 创建时间>5 且 开发者持有占比<0.2
        '高创建时间_高Dev持有': cond1 & cond3,      # 创建时间>5 且 开发者持有占比>0.2
        '低创建时间_低Dev持有': (~cond1) & cond2,   # 创建时间<=5 且 开发者持有占比<0.2
        '低创建时间_高Dev持有': (~cond1) & cond3    # 创建时间<=5 且 开发者持有占比>0.2
    }

    # 统计各组数量
    print("📋 分组统计:")
    total_grouped = 0
    for group_name, condition in groups.items():
        count = condition.sum()
        total_grouped += count
        print(f"   {group_name}: {count} 行")

    print(f"   总计: {total_grouped} 行 (原始: {len(df)} 行)")
    return groups

def apply_excel_formatting_optimized(file_path):
    """优化的Excel格式化，确保所有数值为数字格式"""
    print("🎨 应用Excel格式化和数值转换...")

    try:
        from openpyxl.styles import Alignment, PatternFill
        workbook = load_workbook(file_path)

        for sheet_name in workbook.sheetnames:
            worksheet = workbook[sheet_name]

            # 新增功能1：首列冻结（修改为冻结首列而不是首行）
            worksheet.freeze_panes = 'B1'

            # 新增功能2：首行/列名称行全部左对齐
            if worksheet.max_row > 0:
                for cell in worksheet[1]:  # 第一行（标题行）
                    if cell.value is not None:
                        cell.alignment = Alignment(horizontal='left')

            # 启用筛选
            if worksheet.max_row > 1:
                from openpyxl.utils import get_column_letter
                max_col_letter = get_column_letter(worksheet.max_column)
                worksheet.auto_filter.ref = f"A1:{max_col_letter}{worksheet.max_row}"

            # 新增功能9：ath列所有单元格颜色为绿色
            ath_col_idx = None
            for idx, cell in enumerate(worksheet[1], 1):
                if cell.value == 'ath':
                    ath_col_idx = idx
                    break

            if ath_col_idx:
                green_fill = PatternFill(start_color="00FF00", end_color="00FF00", fill_type="solid")
                ath_col_letter = get_column_letter(ath_col_idx)
                for row in range(1, worksheet.max_row + 1):
                    cell = worksheet[f"{ath_col_letter}{row}"]
                    cell.fill = green_fill
                print(f"   ✅ {sheet_name}: ath列设置为绿色")

            # 强制转换所有数值为数字格式
            numeric_converted = 0
            for row in worksheet.iter_rows(min_row=2, max_row=worksheet.max_row):
                for cell in row:
                    if cell.value is not None:
                        try:
                            # 尝试转换为数字
                            if isinstance(cell.value, str):
                                # 清理字符串中的空格和特殊字符
                                clean_value = str(cell.value).strip()
                                if clean_value and clean_value not in ['', 'nan', 'NaN', 'None']:
                                    try:
                                        # 尝试转换为整数
                                        if '.' not in clean_value:
                                            numeric_value = int(float(clean_value))
                                        else:
                                            numeric_value = float(clean_value)

                                        cell.value = numeric_value
                                        # 设置数值格式
                                        if isinstance(numeric_value, int):
                                            cell.number_format = '0'
                                        else:
                                            cell.number_format = '0.00'
                                        numeric_converted += 1
                                    except (ValueError, TypeError):
                                        # 如果不能转换为数字，保持原值
                                        pass
                            elif isinstance(cell.value, (int, float)):
                                # 已经是数字，确保格式正确
                                if isinstance(cell.value, int):
                                    cell.number_format = '0'
                                else:
                                    cell.number_format = '0.00'
                        except Exception:
                            # 如果处理失败，跳过该单元格
                            pass

            print(f"   ✅ {sheet_name}: 冻结窗格、筛选已设置，{numeric_converted} 个单元格转换为数字格式")

        workbook.save(file_path)
        print("✅ Excel格式化和数值转换完成")

    except Exception as e:
        print(f"❌ Excel格式化失败: {e}")
        import traceback
        traceback.print_exc()

def generate_file_identifier(input_file):
    """生成基于输入文件名的标识符后缀"""
    # 获取文件名（不含路径和扩展名）
    base_name = os.path.splitext(os.path.basename(input_file))[0]
    # 清理文件名，移除常见的处理标识符
    clean_name = base_name.replace('_processed', '').replace('_副本', '').replace('副本', '')
    # 如果清理后的名称过长，截取前10个字符
    if len(clean_name) > 10:
        clean_name = clean_name[:10]
    return clean_name

async def process_excel_optimized(input_file, output_file=None):
    """优化的Excel处理主函数（集成Telegram功能）"""
    print("=== Excel数据处理和分拆工具（优化版）===")
    print("📡 集成Telegram CPW消息搜索功能\n")

    # 生成输入文件标识符
    file_identifier = generate_file_identifier(input_file)
    print(f"📋 输入文件标识符: {file_identifier}")

    # 确定输出文件
    if output_file is None:
        output_file = input_file
        print(f"📁 将覆盖原文件: {input_file}")
    else:
        print(f"📁 输入文件: {input_file}")
        print(f"📁 输出文件: {output_file}")

    # 读取文件
    print(f"\n📖 读取Excel文件...")
    try:
        df = pd.read_excel(input_file)
        print(f"✅ 成功读取: {len(df)} 行 × {len(df.columns)} 列")
    except Exception as e:
        print(f"❌ 读取失败: {e}")
        return False

    # 数据清洗
    df_cleaned = clean_data_optimized(df)

    # Telegram数据处理（在业务分组前执行）
    print(f"\n📡 开始Telegram数据集成...")
    try:
        df_with_telegram = await process_telegram_data(df_cleaned)
        print(f"✅ Telegram数据集成完成")
    except Exception as e:
        print(f"❌ Telegram数据处理失败: {e}")
        print("   ℹ️ 继续使用原始数据进行后续处理...")
        df_with_telegram = df_cleaned

    # ID统计分析（在业务分组前执行，仅对完整数据进行统计）
    id_analysis_results = analyze_id_statistics(df_with_telegram)

    # 创建分组
    try:
        groups = create_business_groups(df_with_telegram)
    except ValueError as e:
        print(f"❌ 分组失败: {e}")
        return False

    # 创建多工作表文件
    print(f"\n📚 创建多工作表Excel文件...")

    # 检查文件是否被占用，如果是则生成新的文件名
    original_output_file = output_file
    counter = 1
    while True:
        try:
            # 尝试创建文件以检查是否可写
            with open(output_file, 'w') as test_file:
                pass
            # 如果成功，删除测试文件并跳出循环
            os.remove(output_file)
            break
        except (PermissionError, OSError) as e:
            print(f"   ⚠️ 文件被占用或无权限: {output_file}")
            # 生成新的文件名
            base_name = os.path.splitext(original_output_file)[0]
            extension = os.path.splitext(original_output_file)[1]
            output_file = f"{base_name}_副本{counter}{extension}"
            counter += 1
            print(f"   🔄 尝试新文件名: {output_file}")

            if counter > 10:  # 避免无限循环
                print(f"   ❌ 尝试了多个文件名仍无法创建，请检查文件权限或关闭相关程序")
                return False

    try:
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 写入完整数据
            df_with_telegram.to_excel(writer, sheet_name='完整数据', index=False)
            print(f"📄 完整数据: {len(df_with_telegram)} 行")

            # 写入分组数据
            for group_name, condition in groups.items():
                group_df = df_with_telegram[condition]
                if len(group_df) > 0:
                    sheet_name = group_name[:31]  # Excel工作表名称限制
                    group_df.to_excel(writer, sheet_name=sheet_name, index=False)
                    print(f"📄 {sheet_name}: {len(group_df)} 行")
                else:
                    print(f"⚠️ 跳过空分组: {group_name}")

            # 写入ID统计分析结果
            if id_analysis_results is not None:
                print(f"📊 添加ID统计分析工作表...")

                # 写入汇总统计表
                summary_df = pd.DataFrame(id_analysis_results['summary_data'])
                summary_df.to_excel(writer, sheet_name='ID统计汇总', index=False)
                print(f"📄 ID统计汇总: {len(summary_df)} 行")

                # 写入详细统计表
                for col_name, results in id_analysis_results['detailed_results'].items():
                    if results:  # 确保有数据
                        df_result = pd.DataFrame(results)
                        # 处理无穷大值
                        df_result['0比1比值'] = df_result['0比1比值'].replace([float('inf')], '∞')

                        # 生成工作表名称（限制长度）
                        sheet_name = f'{col_name}详细统计'[:31]
                        df_result.to_excel(writer, sheet_name=sheet_name, index=False)
                        print(f"📄 {sheet_name}: {len(df_result)} 行")

        print(f"✅ 多工作表文件创建完成")

    except Exception as e:
        print(f"❌ 文件创建失败: {e}")
        return False

    # 应用格式化
    apply_excel_formatting_optimized(output_file)

    # 新增功能：创建独立的业务分组Excel文件
    print(f"\n📁 创建独立业务分组Excel文件...")
    output_dir = os.path.dirname(output_file)

    try:
        for group_name, condition in groups.items():
            group_df = df_with_telegram[condition]
            if len(group_df) > 0:
                # 生成独立文件路径，添加输入文件标识符后缀
                individual_file = os.path.join(output_dir, f"{group_name}_{file_identifier}.xlsx")

                # 创建独立Excel文件
                with pd.ExcelWriter(individual_file, engine='openpyxl') as writer:
                    group_df.to_excel(writer, sheet_name=group_name[:31], index=False)

                # 对独立文件应用相同的格式化
                apply_excel_formatting_optimized(individual_file)

                print(f"📄 {group_name}_{file_identifier}.xlsx: {len(group_df)} 行")
            else:
                print(f"⚠️ 跳过空分组文件: {group_name}")

        print(f"✅ 独立业务分组文件创建完成")

    except Exception as e:
        print(f"❌ 独立文件创建失败: {e}")
        # 不返回False，因为主文件已经成功创建

    # 验证结果
    print(f"\n📊 验证结果...")
    try:
        # 验证主工作簿文件
        excel_file = pd.ExcelFile(output_file)
        print(f"✅ 主工作簿: 成功创建 {len(excel_file.sheet_names)} 个工作表")

        for sheet_name in excel_file.sheet_names:
            sheet_df = pd.read_excel(output_file, sheet_name=sheet_name)
            print(f"   📋 {sheet_name}: {len(sheet_df)} 行")

        # 验证独立业务分组文件
        individual_files_count = 0
        for group_name, condition in groups.items():
            group_df = df_with_telegram[condition]
            if len(group_df) > 0:
                individual_file = os.path.join(output_dir, f"{group_name}_{file_identifier}.xlsx")
                if os.path.exists(individual_file):
                    individual_files_count += 1
                    print(f"   📄 {group_name}_{file_identifier}.xlsx: 文件已创建")

        print(f"✅ 独立文件: 成功创建 {individual_files_count} 个业务分组文件")
        print(f"📁 所有文件保存在: {output_dir}")

        return True

    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

async def main():
    """主函数 - 支持交互式输入（集成Telegram功能）"""
    print("=== Excel数据处理和分拆工具 ===")
    print("📋 功能：数据清洗、业务分组、格式化")
    print("🔧 处理：缺失值替换、K值转换、数值格式化")
    print("📊 分拆：根据创建时间和开发者持有占比自动分拆工作表")
    print("📡 集成：Telegram CPW消息搜索功能\n")

    # 检查是否有命令行参数
    if len(sys.argv) > 1:
        # 命令行模式
        parser = argparse.ArgumentParser(description="Excel数据处理和分拆工具（优化版）")
        parser.add_argument('input_file', help='输入Excel文件路径')
        parser.add_argument('-o', '--output', help='输出Excel文件路径（默认覆盖输入文件）')

        args = parser.parse_args()
        input_file = args.input_file
        output_file = args.output
    else:
        # 交互式模式
        print("📁 请输入Excel文件路径（支持直接粘贴）:")
        print("   提示：可以直接从文件管理器复制文件路径并粘贴")
        print("   示例：C:\\data\\file.xlsx 或 output/merged_data.xlsx\n")

        while True:
            try:
                input_file = input("📂 文件路径: ").strip()

                # 清理路径中的引号
                input_file = input_file.strip('"').strip("'")

                if not input_file:
                    print("⚠️ 请输入文件路径")
                    continue

                if not os.path.exists(input_file):
                    print(f"❌ 文件不存在: {input_file}")
                    print("请检查路径是否正确，然后重新输入\n")
                    continue

                break

            except KeyboardInterrupt:
                print("\n⚠️ 用户取消操作")
                return False

        # 自动生成输出文件路径
        input_dir = os.path.dirname(input_file)
        input_name = os.path.basename(input_file)
        name_without_ext = os.path.splitext(input_name)[0]
        ext = os.path.splitext(input_name)[1]
        output_file = os.path.join(input_dir, f"{name_without_ext}_processed{ext}")
        print(f"\n📤 自动保存到: {output_file}")

    # 确认处理
    print(f"\n🔄 准备处理文件...")
    print(f"📂 输入文件: {input_file}")
    if output_file:
        print(f"📁 输出文件: {output_file}")
    else:
        print(f"📁 输出方式: 覆盖原文件")

    # 执行处理
    success = await process_excel_optimized(input_file, output_file)

    if success:
        print(f"\n🎉 处理完成！")
        result_file = output_file or input_file
        print(f"📁 结果文件: {result_file}")
        print(f"💡 请打开Excel文件查看分拆结果和格式化效果")

        # 询问是否打开文件
        if len(sys.argv) <= 1:  # 只在交互模式下询问
            try:
                open_file = input(f"\n是否打开结果文件？(y/N): ").strip().lower()
                if open_file in ['y', 'yes']:
                    try:
                        os.startfile(result_file)  # Windows
                        print("✅ 文件已打开")
                    except:
                        print("⚠️ 无法自动打开文件，请手动打开")
            except KeyboardInterrupt:
                pass
    else:
        print(f"\n❌ 处理失败！")

    return success

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


'''
山丘之王时间戳、进程1阶段、创建时间、开发者持有占比 列的空值统一替换为 -1
描述 列的空值替换为 2
DexPaid支付时间 列的空值替换为 -1

Top 1 持有比例 列中17-25范围内的值会被替换为空值

Top 1 持有比例 → 17-25范围内的值替换为空值
1m涨跌幅 → 保留负号，去掉正号，处理K值转换，去掉%和>符号
Buys、Sells 列的K值自动转换为纯数字（乘以1000）

added_time 列右边自动新增 时间段 列
时间段计算：每3小时为一个时间段（0-7共8个时间段）

ath 列左边新增 ath比值 列（ath/atl_bf_ath）
ath 列右边新增 athh 列（分类：0-2）≤165: 分类0 , 166-699: 分类2 , ≥700: 分类1

 类别编码转换
网站 → 编码：0=空值，1=包含app，2=包含pump.fun/bit.ly/discord/t.co/t.me，3=包含社交媒体，4=其他
Twitter → 编码：0=空值，1=包含/status，2=包含i/communities，3=其他twitter/x.com，4=其他
Telegram → 编码：0=空值，1=包含https://t.me，2=其他
📋 规则化空值处理
KOL/VC → 空值替换为 0
开发者 → 空值替换为 1
开发者(DEV) → 空值替换为 1
鲸鱼 → 空值替换为 0
巨鲸 → 空值替换为 0
新钱包 → 空值替换为 0
狙击者 → 空值替换为 0
持币大户 → 空值替换为中位数
老鼠仓 → 空值替换为 0
老鼠仓2 → 空值替换为 1
持有者数量 → 空值替换为中位数
老鼠仓百分比 → 空值替换为中位数
跑路百分比 → 空值替换为 0
跑路比例 → 空值替换为 0
Top50状态 → 空值替换为中位数
24H换手率 → 空值替换为中位数
钓鱼地址 → 空值替换为中位数
阴谋集团 → 空值替换为中位数
🎨 格式化处理
ath列 → 设置绿色背景色
所有数值列 → 强制转换为数字格式
标题行 → 左对齐
工作表 → 冻结首列（B1位置）
全表 → 启用自动筛选
📊 业务分组
根据创建时间和开发者持有占比两个维度创建4个工作表：

高创建时间_低Dev持有（创建时间>5 且 开发者持有占比<0.2）
高创建时间_高Dev持有（创建时间>5 且 开发者持有占比≥0.2）
低创建时间_低Dev持有（创建时间≤5 且 开发者持有占比<0.2）
低创建时间_高Dev持有（创建时间≤5 且 开发者持有占比≥0.2）
'''