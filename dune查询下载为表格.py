# -*- coding: utf-8 -*-
"""Copy of Dune API Quickstart.ipynb

Automatically generated by <PERSON>b.

Original file is located at
    https://colab.research.google.com/drive/1bU9YAGe1T7BtXXTkfewoAGcyXF7VnA9_

# About

- This Python notebook is a quickstart for using Dune API to fetch data. 📶

- Input you API key to run this quickstart 🔑. Replace the query ID to fetch data for your query ⌨️.

---

## Set up
- Install Dune API SDK (run `pip install dune_client`)
- Obtain an API key (https://docs.dune.com/api-reference/overview/authentication)
"""

#!pip install dune_client
#!pip install dataclasses_json

from dune_client.types import QueryParameter
from dune_client.client import DuneClient
from dune_client.query import QueryBase

"""### Input our Dune API key here and query ID here 👇"""

dune_api_key = 'aoIRZzGSZaHhJy8gTkHDeDpDYSMUQcDs' # input your API key, to create one follow guide here https://docs.dune.com/api-reference/overview/authentication
query_id = 5568167

dune = DuneClient(
    api_key=dune_api_key,
    base_url="https://api.dune.com",
    request_timeout=(300) # request will time out after 300 seconds
)

"""## Get latest result for [Active Farcaster User Stats query](https://dune.com/queries/3418402)



"""

query_result = dune.get_latest_result_dataframe(
    query=query_id
    # # filter for users account more than a month old and more than bottom active tier
    # , filters="account_age > 30 and fid_active_tier > 1"
    # # sort result by number of channels they are follow in descending order
    # , sort_by=["channels desc"]
)

query_result

# prompt: 生成代码保存当前输出结果为csv格式，不要修改之前代码，直接生成新的后续代码

# 新增时间差计算功能
import pandas as pd
from datetime import datetime
import numpy as np

def calculate_time_differences(df):
    """
    计算时间差值并添加新列

    Args:
        df: 包含时间列的DataFrame

    Returns:
        df: 添加了时间差列的DataFrame
    """
    print("开始计算时间差...")

    # 检查必需的列是否存在
    required_columns = ['ath_time', 'atl_time', 'added_time']
    missing_columns = [col for col in required_columns if col not in df.columns]

    if missing_columns:
        print(f"警告：缺少以下列，将跳过时间差计算: {missing_columns}")
        return df

    # 转换时间列为datetime格式
    def parse_time_column(series, col_name):
        """解析时间列，支持多种格式"""
        try:
            # 尝试直接转换
            return pd.to_datetime(series, errors='coerce')
        except Exception as e:
            print(f"转换{col_name}列时出错: {e}")
            return series

    # 转换时间列
    df['ath_time_parsed'] = parse_time_column(df['ath_time'], 'ath_time')
    df['atl_time_parsed'] = parse_time_column(df['atl_time'], 'atl_time')
    df['added_time_parsed'] = parse_time_column(df['added_time'], 'added_time')

    # 计算时间差（秒）
    def calculate_seconds_diff(time1, time2):
        """计算两个时间之间的秒数差值"""
        try:
            if pd.isna(time1) or pd.isna(time2):
                return np.nan
            diff = (time1 - time2).total_seconds()
            return int(diff)  # 返回整数秒
        except Exception:
            return np.nan

    # 计算ath_time - added_time的时间差
    df['ath_time_diff_seconds'] = df.apply(
        lambda row: calculate_seconds_diff(row['ath_time_parsed'], row['added_time_parsed']),
        axis=1
    )

    # 计算atl_time - added_time的时间差
    df['atl_time_diff_seconds'] = df.apply(
        lambda row: calculate_seconds_diff(row['atl_time_parsed'], row['added_time_parsed']),
        axis=1
    )

    # 删除临时的解析列
    df = df.drop(['ath_time_parsed', 'atl_time_parsed', 'added_time_parsed'], axis=1)

    # 统计计算结果
    ath_valid = df['ath_time_diff_seconds'].notna().sum()
    atl_valid = df['atl_time_diff_seconds'].notna().sum()
    total_rows = len(df)

    print(f"时间差计算完成:")
    print(f"- 总行数: {total_rows}")
    print(f"- ATH时间差有效值: {ath_valid}")
    print(f"- ATL时间差有效值: {atl_valid}")

    return df

# 处理查询结果
print("正在处理查询结果...")
processed_result = calculate_time_differences(query_result)

# 保存增强后的结果
output_filename = 'dune_query_result_enhanced_2025.csv'
processed_result.to_csv(output_filename, index=False)
print(f"增强版数据已保存到: {output_filename}")

# 显示新增列的示例数据
if 'ath_time_diff_seconds' in processed_result.columns:
    print("\n新增列示例数据:")
    print("ATH时间差 (前5行):")
    print(processed_result[['ath_time', 'added_time', 'ath_time_diff_seconds']].head())
    print("\nATL时间差 (前5行):")
    print(processed_result[['atl_time', 'added_time', 'atl_time_diff_seconds']].head())