import pandas as pd
import numpy as np
import os
from datetime import datetime

def parse_interval_constraints(interval_text):
    """
    解析特征区间文本，支持多种比较操作符

    Args:
        interval_text (str): 特征区间文本

    Returns:
        list: 约束条件列表，每个元素为 (feature_name, operator, value) 或 (feature_name, 'between', min_val, max_val)
    """
    constraints = []

    # 按行分割
    lines = interval_text.strip().split('\n')

    for line in lines:
        line = line.strip()
        if not line or line.startswith('特征区间:') or line.startswith('---'):
            continue

        # 移除开头的各种前缀
        prefixes = ['.- ', '- ', '    - ', '    .- ']
        found_prefix = False
        for prefix in prefixes:
            if line.startswith(prefix):
                line = line[len(prefix):].strip()
                found_prefix = True
                break

        if not found_prefix:
            continue

        # print(f"解析行: '{line}'")  # 调试信息已关闭

        # 解析不同格式的约束
        try:
            # 检查双边约束（最复杂的情况先处理）
            if '<=' in line and line.count('<=') == 2:
                # 格式：min <= feature <= max
                parts = line.split('<=')
                if len(parts) == 3:
                    min_val = float(parts[0].strip())
                    feature_name = parts[1].strip()
                    max_val = float(parts[2].strip())
                    constraints.append((feature_name, 'between', min_val, max_val))
                    # print(f"  -> 双边约束: {min_val} <= {feature_name} <= {max_val}")
                    continue

            elif '<' in line and line.count('<') == 2 and '<=' not in line:
                # 格式：min < feature < max
                parts = line.split('<')
                if len(parts) == 3:
                    min_val = float(parts[0].strip())
                    feature_name = parts[1].strip()
                    max_val = float(parts[2].strip())
                    constraints.append((feature_name, 'between_exclusive', min_val, max_val))
                    # print(f"  -> 双边约束(不含边界): {min_val} < {feature_name} < {max_val}")
                    continue

            # 单个操作符的情况
            operators = ['<=', '>=', '<', '>', '==', '=', '!=']
            parsed = False

            for op in operators:
                if op in line:
                    if op == '=' and ('==' in line or '!=' in line or '<=' in line or '>=' in line):
                        continue  # 跳过复合操作符的一部分

                    parts = line.split(op)
                    if len(parts) == 2:
                        left_part = parts[0].strip()
                        right_part = parts[1].strip()

                        # 判断哪边是特征名，哪边是数值
                        try:
                            # 尝试左边是数值，右边是特征名
                            value = float(left_part)
                            feature_name = right_part

                            # 转换操作符方向
                            if op == '<=':
                                constraints.append((feature_name, '>=', value))
                            elif op == '<':
                                constraints.append((feature_name, '>', value))
                            elif op == '>=':
                                constraints.append((feature_name, '<=', value))
                            elif op == '>':
                                constraints.append((feature_name, '<', value))
                            elif op in ['==', '=']:
                                constraints.append((feature_name, '==', value))
                            elif op == '!=':
                                constraints.append((feature_name, '!=', value))
                            parsed = True
                            break

                        except ValueError:
                            # 左边不是数值，尝试右边是数值，左边是特征名
                            try:
                                value = float(right_part)
                                feature_name = left_part
                                constraints.append((feature_name, op, value))
                                parsed = True
                                break
                            except ValueError:
                                continue

            # 检查类别特征约束：feature: IN {value1, value2, ...}
            if ': IN {' in line and '}' in line:
                try:
                    # 解析类别约束格式：feature_name: IN {value1, value2, value3}
                    parts = line.split(': IN {')
                    if len(parts) == 2:
                        feature_name = parts[0].strip()
                        values_part = parts[1].strip()
                        if values_part.endswith('}'):
                            values_part = values_part[:-1]  # 移除结尾的 }

                            # 解析值列表
                            values = []
                            for val_str in values_part.split(','):
                                val_str = val_str.strip()
                                try:
                                    # 尝试转换为数字
                                    if '.' in val_str:
                                        values.append(float(val_str))
                                    else:
                                        values.append(int(val_str))
                                except ValueError:
                                    # 如果不是数字，保持为字符串
                                    values.append(val_str.strip('\'"'))

                            constraints.append((feature_name, 'in', values))
                            parsed = True
                            # print(f"  -> 类别约束: {feature_name} IN {values}")
                except Exception as e:
                    print(f"  -> 类别约束解析错误: {e}")

            if not parsed:
                print(f"  -> 跳过无法解析的行: {line}")

        except Exception as e:
            print(f"  -> 解析错误: {e}")
            continue

    print(f"\n解析完成，共识别出 {len(constraints)} 个约束条件")
    return constraints

def apply_constraints_to_dataframe(df, constraints):
    """
    根据约束条件筛选DataFrame

    Args:
        df (pd.DataFrame): 原始数据
        constraints (list): 约束条件列表，每个元素为 (feature_name, operator, value) 或 (feature_name, 'between', min_val, max_val)

    Returns:
        tuple: (筛选后的DataFrame, 满足条件的行索引)
    """
    # 创建初始掩码（所有行都为True）
    mask = pd.Series([True] * len(df), index=df.index)

    applied_constraints = []

    for constraint in constraints:
        if len(constraint) == 3:
            feature_name, operator, value = constraint
        elif len(constraint) == 4:
            feature_name, operator, min_val, max_val = constraint
        else:
            continue

        if feature_name not in df.columns:
            print(f"警告: 特征 '{feature_name}' 不在数据中，跳过此约束")
            continue

        # 检查并处理数据类型
        feature_series = df[feature_name].copy()

        # 对于数值比较操作，需要确保数据是数值类型
        if operator in ['<=', '>=', '<', '>', 'between', 'between_exclusive']:
            try:
                # 尝试将特征列转换为数值类型
                feature_series = pd.to_numeric(feature_series, errors='coerce')

                # 检查转换后是否有NaN值（表示原来是非数值）
                nan_count = feature_series.isna().sum()
                if nan_count > 0:
                    print(f"警告: 特征 '{feature_name}' 中有 {nan_count} 个非数值数据，这些行将被排除在比较之外")

            except Exception as e:
                print(f"警告: 无法将特征 '{feature_name}' 转换为数值类型: {e}，跳过此约束")
                continue

        feature_mask = pd.Series([True] * len(df), index=df.index)

        try:
            # 应用不同类型的约束
            if operator == 'between':
                # 双边约束（包含边界）
                valid_mask = ~feature_series.isna()
                feature_mask &= valid_mask & (feature_series >= min_val) & (feature_series <= max_val)
                constraint_desc = f"{min_val} <= {feature_name} <= {max_val}"
            elif operator == 'between_exclusive':
                # 双边约束（不含边界）
                valid_mask = ~feature_series.isna()
                feature_mask &= valid_mask & (feature_series > min_val) & (feature_series < max_val)
                constraint_desc = f"{min_val} < {feature_name} < {max_val}"
            elif operator == '<=':
                valid_mask = ~feature_series.isna()
                feature_mask &= valid_mask & (feature_series <= value)
                constraint_desc = f"{feature_name} <= {value}"
            elif operator == '>=':
                valid_mask = ~feature_series.isna()
                feature_mask &= valid_mask & (feature_series >= value)
                constraint_desc = f"{feature_name} >= {value}"
            elif operator == '<':
                valid_mask = ~feature_series.isna()
                feature_mask &= valid_mask & (feature_series < value)
                constraint_desc = f"{feature_name} < {value}"
            elif operator == '>':
                valid_mask = ~feature_series.isna()
                feature_mask &= valid_mask & (feature_series > value)
                constraint_desc = f"{feature_name} > {value}"
            elif operator in ['==', '=']:
                # 等值比较可以处理字符串和数值
                feature_mask &= (df[feature_name] == value)
                constraint_desc = f"{feature_name} == {value}"
            elif operator == '!=':
                # 不等值比较可以处理字符串和数值
                feature_mask &= (df[feature_name] != value)
                constraint_desc = f"{feature_name} != {value}"
            elif operator == 'in':
                # 类别约束：特征值必须在指定的值列表中
                feature_mask &= df[feature_name].isin(value)  # 这里value是值列表
                constraint_desc = f"{feature_name} IN {{{', '.join(map(str, value))}}}"
            else:
                print(f"警告: 不支持的操作符 '{operator}'，跳过此约束")
                continue

        except Exception as e:
            print(f"警告: 应用约束 '{feature_name} {operator} {value if len(constraint) == 3 else f'{min_val}-{max_val}'}' 时发生错误: {e}，跳过此约束")
            continue

        mask &= feature_mask
        applied_constraints.append(constraint_desc)

    # 筛选数据
    filtered_df = df[mask].copy()
    matching_indices = df.index[mask].tolist()

    print(f"\n应用的约束条件:")
    for constraint in applied_constraints:
        print(f"  - {constraint}")

    print(f"\n筛选结果:")
    print(f"  原始样本数: {len(df)}")
    print(f"  满足条件的样本数: {len(filtered_df)}")
    print(f"  筛选比例: {len(filtered_df)/len(df)*100:.2f}%")

    return filtered_df, matching_indices

def save_filtered_results(original_file_path, filtered_df, matching_indices, constraints):
    """
    保存筛选结果到新的Excel文件

    Args:
        original_file_path (str): 原始文件路径
        filtered_df (pd.DataFrame): 筛选后的数据
        matching_indices (list): 满足条件的行索引
        constraints (dict): 约束条件
    """
    # 生成输出文件名
    base_name = os.path.splitext(original_file_path)[0]
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"{base_name}_筛选结果_{timestamp}.xlsx"

    # 创建Excel写入器
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 写入筛选后的数据（不包含索引列）
        filtered_df.to_excel(writer, sheet_name='筛选结果', index=False)

        # 创建约束条件说明表
        constraint_info = []
        for constraint in constraints:
            if len(constraint) == 3:
                feature_name, operator, value = constraint
                if operator == 'in':
                    # 类别约束
                    constraint_info.append([feature_name, f"{feature_name} IN {{{', '.join(map(str, value))}}}"])
                else:
                    # 普通约束
                    constraint_info.append([feature_name, f"{feature_name} {operator} {value}"])
            elif len(constraint) == 4:
                feature_name, operator, min_val, max_val = constraint
                if operator == 'between':
                    constraint_info.append([feature_name, f"{min_val} <= {feature_name} <= {max_val}"])
                elif operator == 'between_exclusive':
                    constraint_info.append([feature_name, f"{min_val} < {feature_name} < {max_val}"])

        constraint_df = pd.DataFrame(constraint_info, columns=['特征名称', '约束条件'])
        constraint_df.to_excel(writer, sheet_name='约束条件', index=False)

        # 获取原始文件的行数（处理编码问题）
        try:
            if original_file_path.endswith('.csv'):
                # 尝试多种编码格式读取CSV文件
                for encoding in ['utf-8', 'gbk', 'gb2312', 'latin1']:
                    try:
                        original_df = pd.read_csv(original_file_path, encoding=encoding)
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    # 如果所有编码都失败，使用错误处理方式
                    original_df = pd.read_csv(original_file_path, encoding='utf-8', errors='ignore')
            else:
                original_df = pd.read_excel(original_file_path)
            original_count = len(original_df)
        except Exception as e:
            print(f"警告: 无法读取原始文件获取样本数，使用筛选后的数据计算: {e}")
            original_count = len(filtered_df)

        # 创建统计信息表
        stats_info = [
            ['原始样本数', original_count],
            ['满足条件的样本数', len(filtered_df)],
            ['筛选比例', f"{len(filtered_df)/original_count*100:.2f}%" if original_count > 0 else "0.00%"],
            ['生成时间', datetime.now().strftime("%Y-%m-%d %H:%M:%S")]
        ]
        stats_df = pd.DataFrame(stats_info, columns=['统计项', '值'])
        stats_df.to_excel(writer, sheet_name='统计信息', index=False)

        # 应用Excel格式设置：首行冻结和自动筛选
        workbook = writer.book

        # 设置筛选结果表格格式
        worksheet_results = writer.sheets['筛选结果']
        # 首行冻结（冻结第一行）
        worksheet_results.freeze_panes = 'A2'
        # 自动筛选（为整个数据区域添加筛选器）
        if len(filtered_df) > 0:
            max_row = len(filtered_df) + 1  # +1 因为包含标题行
            max_col = len(filtered_df.columns)
            # 将列号转换为Excel列字母（支持超过26列的情况）
            def get_column_letter(col_num):
                result = ""
                while col_num > 0:
                    col_num -= 1
                    result = chr(col_num % 26 + ord('A')) + result
                    col_num //= 26
                return result

            max_col_letter = get_column_letter(max_col)
            worksheet_results.auto_filter.ref = f'A1:{max_col_letter}{max_row}'
            print(f"已为筛选结果表格设置自动筛选范围: A1:{max_col_letter}{max_row}")

        # 设置约束条件表格格式
        worksheet_constraints = writer.sheets['约束条件']
        worksheet_constraints.freeze_panes = 'A2'
        if len(constraint_df) > 0:
            max_row_constraints = len(constraint_df) + 1
            worksheet_constraints.auto_filter.ref = f'A1:B{max_row_constraints}'

        # 设置统计信息表格格式
        worksheet_stats = writer.sheets['统计信息']
        worksheet_stats.freeze_panes = 'A2'
        if len(stats_df) > 0:
            max_row_stats = len(stats_df) + 1
            worksheet_stats.auto_filter.ref = f'A1:B{max_row_stats}'

    print(f"\n结果已保存到: {output_file}")
    print("✅ 已设置首行冻结和自动筛选功能")
    return output_file

def main():
    """
    主函数
    """
    print("=== 特征区间筛选器 ===\n")

    # 输入文件路径
    file_path = input("请输入数据文件路径 (支持 .csv 或 .xlsx): ").strip().strip('"\'').strip()

    # 使用预定义的特征区间（可以直接粘贴替换为新的约束组合）
    interval_text = """
特征区间:
    - DexPaid支付时间 <= 8268
    - 67 <= s77
    - 内幕数 <= 12
    - 内幕占比 <= 6.0000
    - top 10占比 <= 47.8000
    - -10.0000 <= 1m Change <= 0.1100
    - 开发者持有占比 <= 0.0000
    - 0.4000 <= 打包者持有占比 <= 59.8000
    - 狙击手持有占比 <= 5.9
    - 前10持有者占比 <= 30.4000
    - 前100持有者占比 <= 167.7000
    - 0 <= 总配对费用
    - 157 <= 平均持有余额
    - 智能钱包 <= 2
    - 创建者钱包 <= 9
    - 老鼠交易者钱包 <= 8
    - 5 <= 顶级钱包 <= 174
    - 打包者钱包 <= 198
    - Twitter变更次数 <= 2.0000
    - 58005.0000 <= 当前价格
    - 1分钟价格 <= 77167.0000
    - 1分钟买入次数 <= 249.0000
    - 1分钟卖出次数 <= 164.0000
    - 2034.0000 <= 1分钟买入量
    - 29.0000 <= 5分钟买入次数 <= 658.0000
    - 5分钟卖出次数 <= 342.0000
    - 4240.0000 <= 5分钟交易量 <= 93424.0000
    - 3182.0000 <= 5分钟买入量
    - 55.8000 <= bought_rate <= 117.3000
    - 76.0000 <= 符合条件的钱包数量
    - 2.2000 <= 最大持有者占比 <= 8.5000
    - GMGN持有者数量 <= 353
    - 4 <= 6的个数
    - 14 <= null统计
    - sandwich_bot次数 <= 1.0000
    - Telegram_编码: IN {0}
    """

    if not os.path.exists(file_path):
        print(f"错误: 文件 '{file_path}' 不存在")
        return

    try:
        # 读取数据
        print(f"\n正在读取文件: {file_path}")
        if file_path.endswith('.csv'):
            # 尝试多种编码格式读取CSV文件
            for encoding in ['utf-8', 'gbk', 'gb2312', 'latin1']:
                try:
                    df = pd.read_csv(file_path, encoding=encoding)
                    print(f"成功使用 {encoding} 编码读取文件")
                    break
                except UnicodeDecodeError:
                    continue
            else:
                # 如果所有编码都失败，使用错误处理方式
                print("警告: 使用默认编码读取文件，可能存在字符显示问题")
                df = pd.read_csv(file_path, encoding='utf-8', errors='ignore')
        elif file_path.endswith('.xlsx') or file_path.endswith('.xls'):
            df = pd.read_excel(file_path)
        else:
            print("错误: 不支持的文件格式，请使用 .csv 或 .xlsx 文件")
            return

        # 清理列名
        df.columns = df.columns.str.strip()
        print(f"数据加载完成: {df.shape[0]} 行, {df.shape[1]} 列")

        # 解析约束条件
        print("\n解析特征区间约束...")
        constraints = parse_interval_constraints(interval_text)

        if not constraints:
            print("错误: 未能解析出有效的约束条件")
            return

        # 应用约束条件
        filtered_df, matching_indices = apply_constraints_to_dataframe(df, constraints)

        if len(filtered_df) == 0:
            print("\n警告: 没有样本满足所有约束条件")
            return

        # 保存结果
        output_file = save_filtered_results(file_path, filtered_df, matching_indices, constraints)

        print(f"\n筛选完成！")
        print(f"满足条件的样本索引: {matching_indices[:10]}{'...' if len(matching_indices) > 10 else ''}")

    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
