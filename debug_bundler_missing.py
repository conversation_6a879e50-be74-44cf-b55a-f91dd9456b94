#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import importlib.util
import json

# 导入workflowauto模块
spec = importlib.util.spec_from_file_location("workflowauto", "workflowauto(无需config).py")
workflowauto = importlib.util.module_from_spec(spec)
spec.loader.exec_module(workflowauto)

print("=== 调试bundler次数字段缺失问题 ===")

try:
    # 读取JSON文件
    with open('data/telegram_graduation_messages_20250802_183022.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    messages = data['messages']
    print(f"总消息数: {len(messages)}")
    
    # 分析字段提取情况
    problem_fields = ["transfer_in次数", "is_new次数", "is_suspicious次数", "sandwich_bot次数", "bundler次数"]
    
    # 统计数据
    field_stats = {field: {"有值": 0, "null值": 0, "缺失": 0} for field in problem_fields}
    
    # 特别关注的情况：transfer_in次数有值但bundler次数缺失的消息
    problematic_messages = []
    
    for i, msg_data in enumerate(messages):
        try:
            # 提取文本
            if 'text' in msg_data and isinstance(msg_data['text'], list):
                text_array = msg_data['text']
            else:
                text_array = [str(msg_data['text'])]
            
            # 调用parse_telegram_message
            extracted_data = workflowauto.parse_telegram_message(text_array)
            
            # 统计每个字段的情况
            for field in problem_fields:
                if field in extracted_data:
                    if extracted_data[field] is not None:
                        field_stats[field]["有值"] += 1
                    else:
                        field_stats[field]["null值"] += 1
                else:
                    field_stats[field]["缺失"] += 1
            
            # 检查问题情况：transfer_in次数有值但bundler次数缺失
            transfer_in_has_value = (
                "transfer_in次数" in extracted_data and 
                extracted_data["transfer_in次数"] is not None
            )
            bundler_missing = (
                "bundler次数" not in extracted_data or 
                extracted_data["bundler次数"] is None
            )
            
            if transfer_in_has_value and bundler_missing:
                # 获取原始文本进行分析
                full_text = ' '.join(text_array) if isinstance(text_array, list) else str(text_array)
                
                # 检查原始文本中是否包含bundler字段
                has_bundler_in_text = "bundler 出现次数" in full_text
                
                problematic_messages.append({
                    "消息索引": i + 1,
                    "transfer_in次数": extracted_data.get("transfer_in次数"),
                    "bundler次数": extracted_data.get("bundler次数", "缺失"),
                    "原文包含bundler字段": has_bundler_in_text,
                    "代币地址": extracted_data.get("代币地址", "未知")[:20] + "..." if extracted_data.get("代币地址") else "未知"
                })
        
        except Exception as e:
            print(f"处理消息{i+1}时出错: {e}")
    
    # 输出统计结果
    print(f"\n=== 字段统计结果 ===")
    for field, stats in field_stats.items():
        total = stats["有值"] + stats["null值"] + stats["缺失"]
        print(f"{field}:")
        print(f"  有值: {stats['有值']} ({stats['有值']/total*100:.1f}%)")
        print(f"  null值: {stats['null值']} ({stats['null值']/total*100:.1f}%)")
        print(f"  缺失: {stats['缺失']} ({stats['缺失']/total*100:.1f}%)")
    
    # 输出问题消息
    print(f"\n=== 问题消息分析 ===")
    print(f"transfer_in次数有值但bundler次数缺失的消息数: {len(problematic_messages)}")
    
    if problematic_messages:
        print(f"\n前10个问题消息:")
        for i, msg in enumerate(problematic_messages[:10]):
            print(f"{i+1}. 消息{msg['消息索引']}: transfer_in={msg['transfer_in次数']}, bundler={msg['bundler次数']}, 原文有bundler字段={msg['原文包含bundler字段']}, 代币={msg['代币地址']}")
        
        # 统计原文中包含bundler字段但提取失败的情况
        text_has_bundler = sum(1 for msg in problematic_messages if msg['原文包含bundler字段'])
        print(f"\n其中原文包含bundler字段但提取失败的: {text_has_bundler}/{len(problematic_messages)}")
        
        if text_has_bundler > 0:
            print("这表明正则表达式可能有问题！")
        else:
            print("这表明数据源中确实缺少bundler字段。")
    
    # 检查bundler字段缺失最多的原因
    bundler_missing_count = field_stats["bundler次数"]["缺失"] + field_stats["bundler次数"]["null值"]
    total_messages = len(messages)
    print(f"\nbundler次数字段缺失率: {bundler_missing_count}/{total_messages} ({bundler_missing_count/total_messages*100:.1f}%)")
    
except Exception as e:
    print(f"❌ 调试失败: {e}")
    import traceback
    traceback.print_exc()
